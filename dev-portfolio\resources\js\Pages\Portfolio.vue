<template>
  <div style="background-color: #000000; color: #f8fafc;">
    <!-- Sticky Navigation (Hidden initially, shows after hero) -->
    <header
      ref="stickyNav"
      class="fixed top-0 left-0 right-0 z-50 shadow-lg transform -translate-y-full transition-transform duration-300"
      style="background-color: #000000;"
      :class="{ 'translate-y-0': showStickyNav }"
    >
      <div class="max-w-7xl mx-auto flex justify-center items-center p-4">
        <nav class="space-x-8">
          <button @click="scrollToSection('hero')" class="transition-colors" style="color: #cbd5e1;" onmouseover="this.style.color='#3b82f6';" onmouseout="this.style.color='#cbd5e1';">Home</button>
          <button @click="scrollToSection('projects')" class="transition-colors" style="color: #cbd5e1;" onmouseover="this.style.color='#3b82f6';" onmouseout="this.style.color='#cbd5e1';">Projects</button>
          <button @click="scrollToSection('skills')" class="transition-colors" style="color: #cbd5e1;" onmouseover="this.style.color='#3b82f6';" onmouseout="this.style.color='#cbd5e1';">Skills</button>
          <button @click="scrollToSection('contact')" class="transition-colors" style="color: #cbd5e1;" onmouseover="this.style.color='#3b82f6';" onmouseout="this.style.color='#cbd5e1';">Contact</button>
        </nav>
      </div>
    </header>

    <!-- Hero Section - Full Screen -->
    <section id="hero" ref="heroSection" class="relative min-h-screen flex flex-col">

      <!-- Hero Content with Background Image -->
      <div class="flex-1 flex items-center justify-center relative hero-background">
        <!-- Dark overlay for better text readability -->
        <div class="absolute inset-0 bg-black bg-opacity-50"></div>

        <!-- Hero Content -->
        <div class="relative z-10 text-center px-6 max-w-4xl" style="color: #f8fafc;">
          <h2 class="text-5xl md:text-7xl font-bold mb-6 leading-tight hero-title">
            <span ref="heroLine1" class="hero-line hero-line-1">Hello! I am</span>
            <span ref="heroLine2" class="text-transparent bg-clip-text hero-line hero-line-2" style="background: linear-gradient(45deg, #2563eb, #93c5fd); -webkit-background-clip: text; background-clip: text;">
              Isaac Martel.
            </span>
          </h2>
          <p ref="heroSubtitle" class="text-xl md:text-2xl mb-8 leading-relaxed hero-subtitle" style="color: #cbd5e1;">
            I am a software engineer and full-stack web developer.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button @click="scrollToSection('projects')" class="px-8 py-3 rounded-full font-semibold transition-all duration-300" style="background-color: #2563eb; color: #f8fafc; border: 2px solid #2563eb;" onmouseover="this.style.backgroundColor='#1d4ed8'; this.style.borderColor='#1d4ed8';" onmouseout="this.style.backgroundColor='#2563eb'; this.style.borderColor='#2563eb';">
              View My Work
            </button>
            <button @click="scrollToSection('contact')" class="px-8 py-3 rounded-full font-semibold transition-all duration-300" style="background-color: transparent; color: #f8fafc; border: 2px solid #3b82f6;" onmouseover="this.style.backgroundColor='#3b82f6'; this.style.borderColor='#3b82f6';" onmouseout="this.style.backgroundColor='transparent'; this.style.borderColor='#3b82f6';">
              Get In Touch
            </button>
          </div>
        </div>
      </div>

      <!-- Scroll Indicator -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce" style="color: #3b82f6;">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
      </div>
    </section>

    <!-- Main Content -->
    <main style="background-color: #000000;">
      <!-- Projects Section -->
      <section id="projects" class="py-20 px-8">
        <div class="max-w-6xl mx-auto">
          <div class="text-center mb-16">
            <h3 class="text-4xl font-bold mb-4" style="color: #f8fafc;">Featured Projects</h3>
            <p class="text-xl" style="color: #cbd5e1;">Some of my recent work and contributions</p>
          </div>
          <div class="grid md:grid-cols-2 gap-8">
            <div class="p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow" style="background-color: #000000; border: 2px solid #333333;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mb-6" style="background-color: #2563eb;">
                <svg class="w-6 h-6" style="color: #f8fafc;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h4 class="font-bold text-xl mb-3" style="color: #f8fafc;">Online Student Clearance System</h4>
              <p class="mb-4" style="color: #cbd5e1;">A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p>
              <div class="flex flex-wrap gap-2">
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #2563eb; color: #f8fafc;">Laravel</span>
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #3b82f6; color: #f8fafc;">Vue.js</span>
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #60a5fa; color: #f8fafc;">MySQL</span>
              </div>
            </div>
            <div class="p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow" style="background-color: #000000; border: 2px solid #333333;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mb-6" style="background-color: #1d4ed8;">
                <svg class="w-6 h-6" style="color: #f8fafc;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
              <h4 class="font-bold text-xl mb-3" style="color: #f8fafc;">Intern Tracker Tool</h4>
              <p class="mb-4" style="color: #cbd5e1;">An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p>
              <div class="flex flex-wrap gap-2">
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #2563eb; color: #f8fafc;">Laravel</span>
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #3b82f6; color: #f8fafc;">JavaScript</span>
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #60a5fa; color: #f8fafc;">Bootstrap</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Skills Section -->
      <section id="skills" class="py-20 px-8" style="background-color: #000000;">
        <div class="max-w-6xl mx-auto">
          <div class="text-center mb-16">
            <h3 class="text-4xl font-bold mb-4" style="color: #f8fafc;">Skills & Technologies</h3>
            <p class="text-xl" style="color: #cbd5e1;">Technologies I work with to bring ideas to life</p>
          </div>
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
            <div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color: #000000; border: 1px solid #333333;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color: #2563eb;">
                <span class="font-bold text-lg" style="color: #f8fafc;">L</span>
              </div>
              <span class="font-semibold" style="color: #f8fafc;">Laravel</span>
            </div>
            <div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color: #000000; border: 1px solid #333333;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color: #3b82f6;">
                <span class="font-bold text-lg" style="color: #f8fafc;">V</span>
              </div>
              <span class="font-semibold" style="color: #f8fafc;">Vue.js</span>
            </div>
            <div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color: #000000; border: 1px solid #333333;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color: #60a5fa;">
                <span class="font-bold text-lg" style="color: #f8fafc;">T</span>
              </div>
              <span class="font-semibold" style="color: #f8fafc;">Tailwind CSS</span>
            </div>
            <div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color: #000000; border: 1px solid #333333;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color: #1d4ed8;">
                <span class="font-bold text-lg" style="color: #f8fafc;">U</span>
              </div>
              <span class="font-semibold" style="color: #f8fafc;">UI/UX</span>
            </div>
            <div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color: #000000; border: 1px solid #333333;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color: #1e40af;">
                <span class="font-bold text-lg" style="color: #f8fafc;">G</span>
              </div>
              <span class="font-semibold" style="color: #f8fafc;">Git</span>
            </div>
          </div>
        </div>
      </section>

      <!-- Contact Section -->
      <section id="contact" class="py-20 px-8" style="background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);">
        <div class="max-w-4xl mx-auto text-center">
          <h3 class="text-4xl font-bold mb-6" style="color: #f8fafc;">Let's Work Together</h3>
          <p class="text-xl mb-8" style="color: #93c5fd;">Ready to bring your ideas to life? Let's discuss your next project.</p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="mailto:<EMAIL>" class="px-8 py-3 rounded-full font-semibold transition-colors" style="background-color: #f8fafc; color: #2563eb;" onmouseover="this.style.backgroundColor='#cbd5e1';" onmouseout="this.style.backgroundColor='#f8fafc';">
              Send Email
            </a>
            <a href="#" class="px-8 py-3 rounded-full font-semibold transition-colors" style="border: 2px solid #f8fafc; color: #f8fafc; background-color: transparent;" onmouseover="this.style.backgroundColor='#f8fafc'; this.style.color='#2563eb';" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#f8fafc';">
              Download Resume
            </a>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'

export default {
  name: 'Portfolio',
  setup() {
    const showStickyNav = ref(false)
    const heroSection = ref(null)
    const stickyNav = ref(null)
    const heroLine1 = ref(null)
    const heroLine2 = ref(null)
    const heroSubtitle = ref(null)

    const handleScroll = () => {
      if (heroSection.value) {
        const heroBottom = heroSection.value.offsetTop + heroSection.value.offsetHeight
        const scrollPosition = window.scrollY + 100 // Add some offset

        showStickyNav.value = scrollPosition >= heroBottom
      }
    }

    onMounted(() => {
      window.addEventListener('scroll', handleScroll)
      handleScroll() // Check initial position

      // Start hero animations after a short delay
      setTimeout(() => {
        initHeroAnimations()
      }, 500)
    })

    onUnmounted(() => {
      window.removeEventListener('scroll', handleScroll)
    })

    // Professional animation function
    const animateElement = (element, delay = 0) => {
      return new Promise((resolve) => {
        setTimeout(() => {
          element.classList.add('animate-in')
          resolve()
        }, delay)
      })
    }

    // Initialize professional hero animations
    const initHeroAnimations = async () => {
      if (heroLine1.value && heroLine2.value && heroSubtitle.value) {
        // Reset animations
        heroLine1.value.classList.remove('animate-in')
        heroLine2.value.classList.remove('animate-in')
        heroSubtitle.value.classList.remove('animate-in')

        // Animate elements sequentially with professional timing
        await animateElement(heroLine1.value, 300)
        await animateElement(heroLine2.value, 600)
        await animateElement(heroSubtitle.value, 1000)
      }
    }

    // Smooth scroll to section function
    const scrollToSection = (sectionId) => {
      const element = document.getElementById(sectionId)
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        })
      }
    }

    return {
      showStickyNav,
      heroSection,
      stickyNav,
      heroLine1,
      heroLine2,
      heroSubtitle,
      scrollToSection
    }
  }
}
</script>

<style scoped>
/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Ensure sections have proper spacing for navigation */
section {
  scroll-margin-top: 80px;
}

/* Blue Color Definitions */
.bg-blue-600 {
  background-color: #2563eb;
}

.text-blue-600 {
  color: #2563eb;
}

.bg-blue-700 {
  background-color: #1d4ed8;
}

.text-blue-700 {
  color: #1d4ed8;
}

/* Hero Background Image */
.hero-background {
  background-image: url('/hero_bg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: scroll;
}

/* Professional Hero Animations */
.hero-title {
  perspective: 1000px;
}

.hero-line {
  opacity: 0;
  transform: translateY(30px) rotateX(15deg);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center bottom;
}

.hero-line.animate-in {
  opacity: 1;
  transform: translateY(0) rotateX(0deg);
}

.hero-line-1 {
  transition-delay: 0s;
}

.hero-line-2 {
  transition-delay: 0.2s;
  transform: translateY(40px) rotateX(20deg) scale(0.95);
  background-size: 200% 200%;
  animation: gradientShift 3s ease-in-out infinite;
}

.hero-line-2.animate-in {
  transform: translateY(0) rotateX(0deg) scale(1);
}

.hero-subtitle {
  opacity: 0;
  transform: translateY(25px) scale(0.98);
  transition: all 0.9s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transition-delay: 0.4s;
}

.hero-subtitle.animate-in {
  opacity: 1;
  transform: translateY(0) scale(1);
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}











/* Code Grid Background */
.code-grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(55, 65, 81, 0.15) 1px, transparent 1px),
    linear-gradient(90deg, rgba(55, 65, 81, 0.15) 1px, transparent 1px);
  background-size: 40px 40px;
  animation: codeGridMove 25s linear infinite;
}

@keyframes codeGridMove {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  50% {
    transform: translate(20px, 20px) rotate(1deg);
  }
  100% {
    transform: translate(40px, 40px) rotate(0deg);
  }
}

/* Binary Particles */
.binary-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.binary-particle {
  position: absolute;
  font-family: 'Courier New', monospace;
  font-weight: bold;
  animation: binaryFloat 18s infinite linear;
  text-shadow: 0 0 5px rgba(59, 130, 246, 0.8);
}

@keyframes binaryFloat {
  0% {
    transform: translateY(100vh) translateX(0px) rotate(0deg);
    opacity: 0;
  }
  5% {
    opacity: 0.8;
  }
  95% {
    opacity: 0.8;
  }
  100% {
    transform: translateY(-100px) translateX(50px) rotate(360deg);
    opacity: 0;
  }
}
</style>
