<template>
  <div class="bg-gray-100 text-gray-800">
    <!-- Sticky Navigation (Hidden initially, shows after hero) -->
    <header
      ref="stickyNav"
      class="fixed top-0 left-0 right-0 z-50 bg-white shadow-lg transform -translate-y-full transition-transform duration-300"
      :class="{ 'translate-y-0': showStickyNav }"
    >
      <div class="max-w-7xl mx-auto flex justify-between items-center p-4">
        <h1 class="text-xl font-bold text-gray-800"></h1>
        <nav class="space-x-6">
          <a href="#hero" class="text-gray-700 hover:text-blue-600 transition-colors">Home</a>
          <a href="#projects" class="text-gray-700 hover:text-blue-600 transition-colors">Projects</a>
          <a href="#skills" class="text-gray-700 hover:text-blue-600 transition-colors">Skills</a>
          <a href="#contact" class="text-gray-700 hover:text-blue-600 transition-colors">Contact</a>
        </nav>
      </div>
    </header>

    <!-- Hero Section - Full Screen -->
    <section id="hero" ref="heroSection" class="relative min-h-screen flex flex-col">

      <!-- Hero Content -->
      <div class="flex-1 flex items-center justify-center bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800">
        <div class="text-center text-white px-6 max-w-4xl">
          <h2 class="text-5xl md:text-7xl font-bold mb-6 leading-tight">
            Hi, I'm a 
            <span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-pink-400">
              Web Developer
            </span>
          </h2>
          <p class="text-xl md:text-2xl mb-8 text-gray-200 leading-relaxed">
            I create modern and responsive web applications using Laravel, Vue, and Tailwind CSS
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="#projects" class="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors">
              View My Work
            </a>
            <a href="#contact" class="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-colors">
              Get In Touch
            </a>
          </div>
        </div>
      </div>

      <!-- Scroll Indicator -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
      </div>
    </section>

    <!-- Main Content -->
    <main class="bg-white">
      <!-- Projects Section -->
      <section id="projects" class="py-20 px-8">
        <div class="max-w-6xl mx-auto">
          <div class="text-center mb-16">
            <h3 class="text-4xl font-bold mb-4 text-gray-800">Featured Projects</h3>
            <p class="text-xl text-gray-600">Some of my recent work and contributions</p>
          </div>
          <div class="grid md:grid-cols-2 gap-8">
            <div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow border border-gray-100">
              <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h4 class="font-bold text-xl mb-3 text-gray-800">Online Student Clearance System</h4>
              <p class="text-gray-600 mb-4">A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p>
              <div class="flex flex-wrap gap-2">
                <span class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full">Laravel</span>
                <span class="px-3 py-1 bg-green-100 text-green-700 text-sm rounded-full">Vue.js</span>
                <span class="px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded-full">MySQL</span>
              </div>
            </div>
            <div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow border border-gray-100">
              <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6">
                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
              <h4 class="font-bold text-xl mb-3 text-gray-800">Intern Tracker Tool</h4>
              <p class="text-gray-600 mb-4">An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p>
              <div class="flex flex-wrap gap-2">
                <span class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full">Laravel</span>
                <span class="px-3 py-1 bg-yellow-100 text-yellow-700 text-sm rounded-full">JavaScript</span>
                <span class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full">Bootstrap</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Skills Section -->
      <section id="skills" class="py-20 px-8 bg-gray-50">
        <div class="max-w-6xl mx-auto">
          <div class="text-center mb-16">
            <h3 class="text-4xl font-bold mb-4 text-gray-800">Skills & Technologies</h3>
            <p class="text-xl text-gray-600">Technologies I work with to bring ideas to life</p>
          </div>
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
            <div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center">
              <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span class="text-red-600 font-bold text-lg">L</span>
              </div>
              <span class="font-semibold text-gray-800">Laravel</span>
            </div>
            <div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center">
              <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span class="text-green-600 font-bold text-lg">V</span>
              </div>
              <span class="font-semibold text-gray-800">Vue.js</span>
            </div>
            <div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center">
              <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span class="text-blue-600 font-bold text-lg">T</span>
              </div>
              <span class="font-semibold text-gray-800">Tailwind CSS</span>
            </div>
            <div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center">
              <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span class="text-yellow-600 font-bold text-lg">U</span>
              </div>
              <span class="font-semibold text-gray-800">UI/UX</span>
            </div>
            <div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center">
              <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span class="text-gray-600 font-bold text-lg">G</span>
              </div>
              <span class="font-semibold text-gray-800">Git</span>
            </div>
          </div>
        </div>
      </section>

      <!-- Contact Section -->
      <section id="contact" class="py-20 px-8 bg-gradient-to-br from-blue-600 to-purple-600">
        <div class="max-w-4xl mx-auto text-center">
          <h3 class="text-4xl font-bold mb-6 text-white">Let's Work Together</h3>
          <p class="text-xl text-blue-100 mb-8">Ready to bring your ideas to life? Let's discuss your next project.</p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="mailto:<EMAIL>" class="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors">
              Send Email
            </a>
            <a href="#" class="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-colors">
              Download Resume
            </a>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'

export default {
  name: 'Portfolio',
  setup() {
    const showStickyNav = ref(false)
    const heroSection = ref(null)
    const stickyNav = ref(null)

    const handleScroll = () => {
      if (heroSection.value) {
        const heroBottom = heroSection.value.offsetTop + heroSection.value.offsetHeight
        const scrollPosition = window.scrollY + 100 // Add some offset

        showStickyNav.value = scrollPosition >= heroBottom
      }
    }

    onMounted(() => {
      window.addEventListener('scroll', handleScroll)
      handleScroll() // Check initial position
    })

    onUnmounted(() => {
      window.removeEventListener('scroll', handleScroll)
    })

    return {
      showStickyNav,
      heroSection,
      stickyNav
    }
  }
}
</script>

<style scoped>
/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Ensure sections have proper spacing for navigation */
section {
  scroll-margin-top: 80px;
}
</style>
