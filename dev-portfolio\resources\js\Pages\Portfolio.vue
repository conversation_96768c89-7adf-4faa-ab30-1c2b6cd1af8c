<template>
  <div style="background-color: #0a0a0a; color: #f8fafc;">
    <!-- Sticky Navigation (Hidden initially, shows after hero) -->
    <header
      ref="stickyNav"
      class="fixed top-0 left-0 right-0 z-50 shadow-lg transform -translate-y-full transition-transform duration-300"
      style="background-color: #1a1a1a;"
      :class="{ 'translate-y-0': showStickyNav }"
    >
      <div class="max-w-7xl mx-auto flex justify-center items-center p-4">
        <nav class="space-x-8">
          <button @click="scrollToSection('hero')" class="transition-colors" style="color: #cbd5e1;" onmouseover="this.style.color='#dc2626';" onmouseout="this.style.color='#cbd5e1';">Home</button>
          <button @click="scrollToSection('projects')" class="transition-colors" style="color: #cbd5e1;" onmouseover="this.style.color='#dc2626';" onmouseout="this.style.color='#cbd5e1';">Projects</button>
          <button @click="scrollToSection('skills')" class="transition-colors" style="color: #cbd5e1;" onmouseover="this.style.color='#dc2626';" onmouseout="this.style.color='#cbd5e1';">Skills</button>
          <button @click="scrollToSection('contact')" class="transition-colors" style="color: #cbd5e1;" onmouseover="this.style.color='#dc2626';" onmouseout="this.style.color='#cbd5e1';">Contact</button>
        </nav>
      </div>
    </header>

    <!-- Hero Section - Full Screen -->
    <section id="hero" ref="heroSection" class="relative min-h-screen flex flex-col">

      <!-- Hero Content with Dark Background and Red Blurs -->
      <div class="flex-1 flex items-center justify-center relative" style="background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);">
        <!-- Red blurred clouds that move randomly -->
        <div class="absolute inset-0 overflow-hidden">
          <!-- Large cloud formation - top left -->
          <div class="absolute top-10 left-10 red-cloud cloud-1">
            <div class="absolute w-32 h-32 bg-red-600 rounded-full blur-3xl" style="opacity: 0.2;"></div>
            <div class="absolute w-24 h-24 bg-red-500 rounded-full blur-3xl" style="opacity: 0.15; left: 60px; top: -20px;"></div>
            <div class="absolute w-28 h-28 bg-red-700 rounded-full blur-3xl" style="opacity: 0.18; left: 20px; top: -15px;"></div>
            <div class="absolute w-20 h-20 bg-red-400 rounded-full blur-2xl" style="opacity: 0.12; left: 80px; top: 10px;"></div>
          </div>

          <!-- Medium cloud - bottom right -->
          <div class="absolute bottom-20 right-20 red-cloud cloud-2">
            <div class="absolute w-24 h-24 bg-red-500 rounded-full blur-3xl" style="opacity: 0.15;"></div>
            <div class="absolute w-20 h-20 bg-red-400 rounded-full blur-2xl" style="opacity: 0.12; left: 40px; top: -12px;"></div>
            <div class="absolute w-18 h-18 bg-red-600 rounded-full blur-3xl" style="opacity: 0.14; left: 10px; top: -10px;"></div>
          </div>

          <!-- Wispy cloud - center -->
          <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 red-cloud cloud-3">
            <div class="absolute w-40 h-20 bg-red-700 rounded-full blur-3xl" style="opacity: 0.1;"></div>
            <div class="absolute w-30 h-15 bg-red-800 rounded-full blur-2xl" style="opacity: 0.08; left: 50px; top: -8px;"></div>
            <div class="absolute w-25 h-12 bg-red-600 rounded-full blur-3xl" style="opacity: 0.09; left: 15px; top: -5px;"></div>
          </div>

          <!-- Floating cloud - top right -->
          <div class="absolute top-1/4 right-1/4 red-cloud cloud-4">
            <div class="absolute w-28 h-16 bg-red-400 rounded-full blur-3xl" style="opacity: 0.12;"></div>
            <div class="absolute w-20 h-12 bg-red-300 rounded-full blur-2xl" style="opacity: 0.09; left: 35px; top: -6px;"></div>
            <div class="absolute w-16 h-10 bg-red-500 rounded-full blur-3xl" style="opacity: 0.11; left: 10px; top: -4px;"></div>
          </div>

          <!-- Drifting cloud - bottom left -->
          <div class="absolute bottom-1/4 left-1/4 red-cloud cloud-5">
            <div class="absolute w-26 h-26 bg-red-800 rounded-full blur-3xl" style="opacity: 0.08;"></div>
            <div class="absolute w-22 h-22 bg-red-900 rounded-full blur-3xl" style="opacity: 0.06; left: 35px; top: -14px;"></div>
            <div class="absolute w-18 h-18 bg-red-600 rounded-full blur-2xl" style="opacity: 0.07; left: 8px; top: -8px;"></div>
          </div>

          <!-- Small accent cloud -->
          <div class="absolute top-3/4 right-10 red-cloud cloud-6">
            <div class="absolute w-16 h-16 bg-red-300 rounded-full blur-2xl" style="opacity: 0.06;"></div>
            <div class="absolute w-12 h-12 bg-red-400 rounded-full blur-2xl" style="opacity: 0.05; left: 20px; top: -6px;"></div>
          </div>
        </div>

        <!-- Hero Content -->
        <div class="relative z-10 text-center px-6 max-w-4xl" style="color: #f8fafc;">
          <h2 class="text-5xl md:text-7xl font-bold mb-6 leading-tight">
            Hello! I am
            <span class="text-transparent bg-clip-text" style="background: linear-gradient(45deg, #dc2626, #fca5a5); -webkit-background-clip: text; background-clip: text;">
              Isaac Martel.
            </span>
          </h2>
          <p class="text-xl md:text-2xl mb-8 leading-relaxed" style="color: #cbd5e1;">
            I am a software engineer and full-stack web developer.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button @click="scrollToSection('projects')" class="px-8 py-3 rounded-full font-semibold transition-all duration-300" style="background-color: #dc2626; color: #f8fafc; border: 2px solid #dc2626;" onmouseover="this.style.backgroundColor='#991b1b'; this.style.borderColor='#991b1b';" onmouseout="this.style.backgroundColor='#dc2626'; this.style.borderColor='#dc2626';">
              View My Work
            </button>
            <button @click="scrollToSection('contact')" class="px-8 py-3 rounded-full font-semibold transition-all duration-300" style="background-color: transparent; color: #f8fafc; border: 2px solid #374151;" onmouseover="this.style.backgroundColor='#374151'; this.style.borderColor='#374151';" onmouseout="this.style.backgroundColor='transparent'; this.style.borderColor='#374151';">
              Get In Touch
            </button>
          </div>
        </div>
      </div>

      <!-- Scroll Indicator -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
      </div>
    </section>

    <!-- Main Content -->
    <main style="background-color: #0a0a0a;">
      <!-- Projects Section -->
      <section id="projects" class="py-20 px-8">
        <div class="max-w-6xl mx-auto">
          <div class="text-center mb-16">
            <h3 class="text-4xl font-bold mb-4" style="color: #f8fafc;">Featured Projects</h3>
            <p class="text-xl" style="color: #cbd5e1;">Some of my recent work and contributions</p>
          </div>
          <div class="grid md:grid-cols-2 gap-8">
            <div class="p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow" style="background-color: #1a1a1a; border: 2px solid #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mb-6" style="background-color: #dc2626;">
                <svg class="w-6 h-6" style="color: #f8fafc;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h4 class="font-bold text-xl mb-3" style="color: #f8fafc;">Online Student Clearance System</h4>
              <p class="mb-4" style="color: #cbd5e1;">A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p>
              <div class="flex flex-wrap gap-2">
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #dc2626; color: #f8fafc;">Laravel</span>
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #374151; color: #f8fafc;">Vue.js</span>
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #64748b; color: #f8fafc;">MySQL</span>
              </div>
            </div>
            <div class="p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow" style="background-color: #1a1a1a; border: 2px solid #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mb-6" style="background-color: #374151;">
                <svg class="w-6 h-6" style="color: #f8fafc;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
              <h4 class="font-bold text-xl mb-3" style="color: #f8fafc;">Intern Tracker Tool</h4>
              <p class="mb-4" style="color: #cbd5e1;">An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p>
              <div class="flex flex-wrap gap-2">
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #dc2626; color: #f8fafc;">Laravel</span>
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #374151; color: #f8fafc;">JavaScript</span>
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #64748b; color: #f8fafc;">Bootstrap</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Skills Section -->
      <section id="skills" class="py-20 px-8" style="background-color: #1a1a1a;">
        <div class="max-w-6xl mx-auto">
          <div class="text-center mb-16">
            <h3 class="text-4xl font-bold mb-4" style="color: #f8fafc;">Skills & Technologies</h3>
            <p class="text-xl" style="color: #cbd5e1;">Technologies I work with to bring ideas to life</p>
          </div>
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
            <div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color: #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color: #dc2626;">
                <span class="font-bold text-lg" style="color: #f8fafc;">L</span>
              </div>
              <span class="font-semibold" style="color: #f8fafc;">Laravel</span>
            </div>
            <div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color: #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color: #374151;">
                <span class="font-bold text-lg" style="color: #f8fafc;">V</span>
              </div>
              <span class="font-semibold" style="color: #f8fafc;">Vue.js</span>
            </div>
            <div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color: #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color: #64748b;">
                <span class="font-bold text-lg" style="color: #f8fafc;">T</span>
              </div>
              <span class="font-semibold" style="color: #f8fafc;">Tailwind CSS</span>
            </div>
            <div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color: #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color: #dc2626;">
                <span class="font-bold text-lg" style="color: #f8fafc;">U</span>
              </div>
              <span class="font-semibold" style="color: #f8fafc;">UI/UX</span>
            </div>
            <div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color: #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color: #374151;">
                <span class="font-bold text-lg" style="color: #f8fafc;">G</span>
              </div>
              <span class="font-semibold" style="color: #f8fafc;">Git</span>
            </div>
          </div>
        </div>
      </section>

      <!-- Contact Section -->
      <section id="contact" class="py-20 px-8" style="background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);">
        <div class="max-w-4xl mx-auto text-center">
          <h3 class="text-4xl font-bold mb-6" style="color: #f8fafc;">Let's Work Together</h3>
          <p class="text-xl mb-8" style="color: #fca5a5;">Ready to bring your ideas to life? Let's discuss your next project.</p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="mailto:<EMAIL>" class="px-8 py-3 rounded-full font-semibold transition-colors" style="background-color: #f8fafc; color: #dc2626;" onmouseover="this.style.backgroundColor='#cbd5e1';" onmouseout="this.style.backgroundColor='#f8fafc';">
              Send Email
            </a>
            <a href="#" class="px-8 py-3 rounded-full font-semibold transition-colors" style="border: 2px solid #f8fafc; color: #f8fafc; background-color: transparent;" onmouseover="this.style.backgroundColor='#f8fafc'; this.style.color='#dc2626';" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#f8fafc';">
              Download Resume
            </a>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'

export default {
  name: 'Portfolio',
  setup() {
    const showStickyNav = ref(false)
    const heroSection = ref(null)
    const stickyNav = ref(null)

    const handleScroll = () => {
      if (heroSection.value) {
        const heroBottom = heroSection.value.offsetTop + heroSection.value.offsetHeight
        const scrollPosition = window.scrollY + 100 // Add some offset

        showStickyNav.value = scrollPosition >= heroBottom
      }
    }

    onMounted(() => {
      window.addEventListener('scroll', handleScroll)
      handleScroll() // Check initial position
    })

    onUnmounted(() => {
      window.removeEventListener('scroll', handleScroll)
    })



    // Smooth scroll to section function
    const scrollToSection = (sectionId) => {
      const element = document.getElementById(sectionId)
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        })
      }
    }

    return {
      showStickyNav,
      heroSection,
      stickyNav,
      scrollToSection
    }
  }
}
</script>

<style scoped>
/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Ensure sections have proper spacing for navigation */
section {
  scroll-margin-top: 80px;
}

/* Navy Blue Color Definitions */
.bg-navy-900 {
  background-color: #1e3a8a;
}

.text-navy-900 {
  color: #1e3a8a;
}

/* Dark Background with Red Blurred Clouds */
.blur-2xl {
  filter: blur(40px);
}

.blur-3xl {
  filter: blur(64px);
}

/* Red cloud containers */
.red-cloud {
  position: relative;
  will-change: transform;
}

/* Custom red blur animations */
/* Random cloud movement animations */
@keyframes cloudDrift1 {
  0% { transform: translate(0, 0) scale(1); }
  25% { transform: translate(30px, -20px) scale(1.05); }
  50% { transform: translate(-15px, 35px) scale(0.95); }
  75% { transform: translate(45px, 10px) scale(1.02); }
  100% { transform: translate(0, 0) scale(1); }
}

@keyframes cloudDrift2 {
  0% { transform: translate(0, 0) scale(1); }
  20% { transform: translate(-25px, 40px) scale(1.08); }
  40% { transform: translate(50px, -15px) scale(0.92); }
  60% { transform: translate(-10px, -30px) scale(1.06); }
  80% { transform: translate(35px, 25px) scale(0.98); }
  100% { transform: translate(0, 0) scale(1); }
}

@keyframes cloudDrift3 {
  0% { transform: translate(0, 0) scale(1); }
  30% { transform: translate(40px, 20px) scale(0.9); }
  60% { transform: translate(-30px, -25px) scale(1.1); }
  100% { transform: translate(0, 0) scale(1); }
}

@keyframes cloudDrift4 {
  0% { transform: translate(0, 0) scale(1); }
  15% { transform: translate(-20px, -35px) scale(1.03); }
  35% { transform: translate(25px, 15px) scale(0.97); }
  55% { transform: translate(-40px, 30px) scale(1.07); }
  75% { transform: translate(15px, -20px) scale(0.94); }
  100% { transform: translate(0, 0) scale(1); }
}

@keyframes cloudDrift5 {
  0% { transform: translate(0, 0) scale(1); }
  40% { transform: translate(35px, -40px) scale(1.04); }
  80% { transform: translate(-25px, 20px) scale(0.96); }
  100% { transform: translate(0, 0) scale(1); }
}

@keyframes cloudDrift6 {
  0% { transform: translate(0, 0) scale(1); }
  50% { transform: translate(-15px, 25px) scale(1.02); }
  100% { transform: translate(0, 0) scale(1); }
}

/* Enhanced red blur effects */
.red-blur-1 {
  animation: redBlurFloat 8s ease-in-out infinite;
}

.red-blur-2 {
  animation: redBlurFloat 10s ease-in-out infinite reverse;
}

.red-blur-3 {
  animation: redBlurPulse 6s ease-in-out infinite;
}

.red-blur-4 {
  animation: redBlurFloat 12s ease-in-out infinite;
}

.red-blur-5 {
  animation: redBlurPulse 9s ease-in-out infinite;
}







/* Code Grid Background */
.code-grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(55, 65, 81, 0.15) 1px, transparent 1px),
    linear-gradient(90deg, rgba(55, 65, 81, 0.15) 1px, transparent 1px);
  background-size: 40px 40px;
  animation: codeGridMove 25s linear infinite;
}

@keyframes codeGridMove {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  50% {
    transform: translate(20px, 20px) rotate(1deg);
  }
  100% {
    transform: translate(40px, 40px) rotate(0deg);
  }
}

/* Binary Particles */
.binary-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.binary-particle {
  position: absolute;
  font-family: 'Courier New', monospace;
  font-weight: bold;
  animation: binaryFloat 18s infinite linear;
  text-shadow: 0 0 5px rgba(59, 130, 246, 0.8);
}

@keyframes binaryFloat {
  0% {
    transform: translateY(100vh) translateX(0px) rotate(0deg);
    opacity: 0;
  }
  5% {
    opacity: 0.8;
  }
  95% {
    opacity: 0.8;
  }
  100% {
    transform: translateY(-100px) translateX(50px) rotate(360deg);
    opacity: 0;
  }
}
</style>
