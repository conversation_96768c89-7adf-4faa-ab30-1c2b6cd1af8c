<template>
  <div style="background-color: #0a0a0a; color: #f8fafc;">
    <!-- Sticky Navigation (Hidden initially, shows after hero) -->
    <header
      ref="stickyNav"
      class="fixed top-0 left-0 right-0 z-50 shadow-lg transform -translate-y-full transition-transform duration-300"
      style="background-color: #1a1a1a;"
      :class="{ 'translate-y-0': showStickyNav }"
    >
      <div class="max-w-7xl mx-auto flex justify-center items-center p-4">
        <nav class="space-x-8">
          <button @click="scrollToSection('hero')" class="transition-colors" style="color: #cbd5e1;" onmouseover="this.style.color='#dc2626';" onmouseout="this.style.color='#cbd5e1';">Home</button>
          <button @click="scrollToSection('projects')" class="transition-colors" style="color: #cbd5e1;" onmouseover="this.style.color='#dc2626';" onmouseout="this.style.color='#cbd5e1';">Projects</button>
          <button @click="scrollToSection('skills')" class="transition-colors" style="color: #cbd5e1;" onmouseover="this.style.color='#dc2626';" onmouseout="this.style.color='#cbd5e1';">Skills</button>
          <button @click="scrollToSection('contact')" class="transition-colors" style="color: #cbd5e1;" onmouseover="this.style.color='#dc2626';" onmouseout="this.style.color='#cbd5e1';">Contact</button>
        </nav>
      </div>
    </header>

    <!-- Hero Section - Full Screen -->
    <section id="hero" ref="heroSection" class="relative min-h-screen flex flex-col">

      <!-- Hero Content with Background Image -->
      <div class="flex-1 flex items-center justify-center relative hero-background">
        <!-- Dark overlay for better text readability -->
        <div class="absolute inset-0 bg-black bg-opacity-50"></div>

        <!-- Hero Content -->
        <div class="relative z-10 text-center px-6 max-w-4xl" style="color: #f8fafc;">
          <h2 class="text-5xl md:text-7xl font-bold mb-6 leading-tight">
            Hello! I am
            <span class="text-transparent bg-clip-text" style="background: linear-gradient(45deg, #dc2626, #fca5a5); -webkit-background-clip: text; background-clip: text;">
              Isaac Martel.
            </span>
          </h2>
          <p class="text-xl md:text-2xl mb-8 leading-relaxed" style="color: #cbd5e1;">
            I am a software engineer and full-stack web developer.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button @click="scrollToSection('projects')" class="px-8 py-3 rounded-full font-semibold transition-all duration-300" style="background-color: #dc2626; color: #f8fafc; border: 2px solid #dc2626;" onmouseover="this.style.backgroundColor='#991b1b'; this.style.borderColor='#991b1b';" onmouseout="this.style.backgroundColor='#dc2626'; this.style.borderColor='#dc2626';">
              View My Work
            </button>
            <button @click="scrollToSection('contact')" class="px-8 py-3 rounded-full font-semibold transition-all duration-300" style="background-color: transparent; color: #f8fafc; border: 2px solid #374151;" onmouseover="this.style.backgroundColor='#374151'; this.style.borderColor='#374151';" onmouseout="this.style.backgroundColor='transparent'; this.style.borderColor='#374151';">
              Get In Touch
            </button>
          </div>
        </div>
      </div>

      <!-- Scroll Indicator -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
      </div>
    </section>

    <!-- Main Content -->
    <main style="background-color: #0a0a0a;">
      <!-- Projects Section -->
      <section id="projects" class="py-20 px-8">
        <div class="max-w-6xl mx-auto">
          <div class="text-center mb-16">
            <h3 class="text-4xl font-bold mb-4" style="color: #f8fafc;">Featured Projects</h3>
            <p class="text-xl" style="color: #cbd5e1;">Some of my recent work and contributions</p>
          </div>
          <div class="grid md:grid-cols-2 gap-8">
            <div class="p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow" style="background-color: #1a1a1a; border: 2px solid #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mb-6" style="background-color: #dc2626;">
                <svg class="w-6 h-6" style="color: #f8fafc;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h4 class="font-bold text-xl mb-3" style="color: #f8fafc;">Online Student Clearance System</h4>
              <p class="mb-4" style="color: #cbd5e1;">A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p>
              <div class="flex flex-wrap gap-2">
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #dc2626; color: #f8fafc;">Laravel</span>
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #374151; color: #f8fafc;">Vue.js</span>
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #64748b; color: #f8fafc;">MySQL</span>
              </div>
            </div>
            <div class="p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow" style="background-color: #1a1a1a; border: 2px solid #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mb-6" style="background-color: #374151;">
                <svg class="w-6 h-6" style="color: #f8fafc;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
              <h4 class="font-bold text-xl mb-3" style="color: #f8fafc;">Intern Tracker Tool</h4>
              <p class="mb-4" style="color: #cbd5e1;">An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p>
              <div class="flex flex-wrap gap-2">
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #dc2626; color: #f8fafc;">Laravel</span>
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #374151; color: #f8fafc;">JavaScript</span>
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #64748b; color: #f8fafc;">Bootstrap</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Skills Section -->
      <section id="skills" class="py-20 px-8" style="background-color: #1a1a1a;">
        <div class="max-w-6xl mx-auto">
          <div class="text-center mb-16">
            <h3 class="text-4xl font-bold mb-4" style="color: #f8fafc;">Skills & Technologies</h3>
            <p class="text-xl" style="color: #cbd5e1;">Technologies I work with to bring ideas to life</p>
          </div>
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
            <div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color: #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color: #dc2626;">
                <span class="font-bold text-lg" style="color: #f8fafc;">L</span>
              </div>
              <span class="font-semibold" style="color: #f8fafc;">Laravel</span>
            </div>
            <div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color: #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color: #374151;">
                <span class="font-bold text-lg" style="color: #f8fafc;">V</span>
              </div>
              <span class="font-semibold" style="color: #f8fafc;">Vue.js</span>
            </div>
            <div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color: #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color: #64748b;">
                <span class="font-bold text-lg" style="color: #f8fafc;">T</span>
              </div>
              <span class="font-semibold" style="color: #f8fafc;">Tailwind CSS</span>
            </div>
            <div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color: #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color: #dc2626;">
                <span class="font-bold text-lg" style="color: #f8fafc;">U</span>
              </div>
              <span class="font-semibold" style="color: #f8fafc;">UI/UX</span>
            </div>
            <div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color: #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color: #374151;">
                <span class="font-bold text-lg" style="color: #f8fafc;">G</span>
              </div>
              <span class="font-semibold" style="color: #f8fafc;">Git</span>
            </div>
          </div>
        </div>
      </section>

      <!-- Contact Section -->
      <section id="contact" class="py-20 px-8" style="background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);">
        <div class="max-w-4xl mx-auto text-center">
          <h3 class="text-4xl font-bold mb-6" style="color: #f8fafc;">Let's Work Together</h3>
          <p class="text-xl mb-8" style="color: #fca5a5;">Ready to bring your ideas to life? Let's discuss your next project.</p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="mailto:<EMAIL>" class="px-8 py-3 rounded-full font-semibold transition-colors" style="background-color: #f8fafc; color: #dc2626;" onmouseover="this.style.backgroundColor='#cbd5e1';" onmouseout="this.style.backgroundColor='#f8fafc';">
              Send Email
            </a>
            <a href="#" class="px-8 py-3 rounded-full font-semibold transition-colors" style="border: 2px solid #f8fafc; color: #f8fafc; background-color: transparent;" onmouseover="this.style.backgroundColor='#f8fafc'; this.style.color='#dc2626';" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#f8fafc';">
              Download Resume
            </a>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'

export default {
  name: 'Portfolio',
  setup() {
    const showStickyNav = ref(false)
    const heroSection = ref(null)
    const stickyNav = ref(null)

    const handleScroll = () => {
      if (heroSection.value) {
        const heroBottom = heroSection.value.offsetTop + heroSection.value.offsetHeight
        const scrollPosition = window.scrollY + 100 // Add some offset

        showStickyNav.value = scrollPosition >= heroBottom
      }
    }

    onMounted(() => {
      window.addEventListener('scroll', handleScroll)
      handleScroll() // Check initial position
    })

    onUnmounted(() => {
      window.removeEventListener('scroll', handleScroll)
    })



    // Smooth scroll to section function
    const scrollToSection = (sectionId) => {
      const element = document.getElementById(sectionId)
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        })
      }
    }

    return {
      showStickyNav,
      heroSection,
      stickyNav,
      scrollToSection
    }
  }
}
</script>

<style scoped>
/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Ensure sections have proper spacing for navigation */
section {
  scroll-margin-top: 80px;
}

/* Navy Blue Color Definitions */
.bg-navy-900 {
  background-color: #1e3a8a;
}

.text-navy-900 {
  color: #1e3a8a;
}

/* Dark Background with Red Blurred Smoke */
.blur-2xl {
  filter: blur(40px);
}

.blur-3xl {
  filter: blur(64px);
}

/* Red smoke containers */
.red-smoke {
  position: relative;
  will-change: transform;
}

.smoke-wisp {
  border-radius: 50% 30% 60% 40%;
  filter: blur(50px);
}

/* Custom red blur animations */
/* Random smoke movement animations */
@keyframes smokeDrift1 {
  0% { transform: translate(0, 0) scale(1) rotate(0deg); opacity: 0.15; }
  25% { transform: translate(40px, -30px) scale(1.1) rotate(15deg); opacity: 0.20; }
  50% { transform: translate(-20px, 50px) scale(0.9) rotate(-10deg); opacity: 0.12; }
  75% { transform: translate(60px, 20px) scale(1.05) rotate(25deg); opacity: 0.18; }
  100% { transform: translate(0, 0) scale(1) rotate(0deg); opacity: 0.15; }
}

@keyframes smokeDrift2 {
  0% { transform: translate(0, 0) scale(1) rotate(0deg); opacity: 0.14; }
  20% { transform: translate(-35px, 45px) scale(1.15) rotate(-20deg); opacity: 0.22; }
  40% { transform: translate(55px, -25px) scale(0.85) rotate(30deg); opacity: 0.10; }
  60% { transform: translate(-15px, -40px) scale(1.08) rotate(-15deg); opacity: 0.19; }
  80% { transform: translate(45px, 35px) scale(0.92) rotate(40deg); opacity: 0.13; }
  100% { transform: translate(0, 0) scale(1) rotate(0deg); opacity: 0.14; }
}

@keyframes smokeDrift3 {
  0% { transform: translate(0, 0) scale(1) rotate(0deg); opacity: 0.08; }
  33% { transform: translate(50px, 30px) scale(0.8) rotate(45deg); opacity: 0.15; }
  66% { transform: translate(-40px, -35px) scale(1.2) rotate(-30deg); opacity: 0.05; }
  100% { transform: translate(0, 0) scale(1) rotate(0deg); opacity: 0.08; }
}

@keyframes smokeDrift4 {
  0% { transform: translate(0, 0) scale(1) rotate(0deg); opacity: 0.11; }
  15% { transform: translate(-30px, -45px) scale(1.12) rotate(-25deg); opacity: 0.18; }
  35% { transform: translate(35px, 25px) scale(0.88) rotate(35deg); opacity: 0.08; }
  55% { transform: translate(-50px, 40px) scale(1.18) rotate(-40deg); opacity: 0.16; }
  75% { transform: translate(25px, -30px) scale(0.82) rotate(20deg); opacity: 0.09; }
  100% { transform: translate(0, 0) scale(1) rotate(0deg); opacity: 0.11; }
}

@keyframes smokeDrift5 {
  0% { transform: translate(0, 0) scale(1) rotate(0deg); opacity: 0.07; }
  40% { transform: translate(45px, -50px) scale(1.25) rotate(50deg); opacity: 0.14; }
  80% { transform: translate(-35px, 30px) scale(0.75) rotate(-35deg); opacity: 0.04; }
  100% { transform: translate(0, 0) scale(1) rotate(0deg); opacity: 0.07; }
}

@keyframes smokeDrift6 {
  0% { transform: translate(0, 0) scale(1) rotate(0deg); opacity: 0.05; }
  50% { transform: translate(-25px, 35px) scale(1.3) rotate(-45deg); opacity: 0.12; }
  100% { transform: translate(0, 0) scale(1) rotate(0deg); opacity: 0.05; }
}

@keyframes smokeDrift7 {
  0% { transform: translate(0, 0) scale(1) rotate(0deg); opacity: 0.06; }
  30% { transform: translate(30px, -25px) scale(0.9) rotate(60deg); opacity: 0.10; }
  70% { transform: translate(-20px, 40px) scale(1.1) rotate(-20deg); opacity: 0.08; }
  100% { transform: translate(0, 0) scale(1) rotate(0deg); opacity: 0.06; }
}

/* Enhanced red blur effects */
.red-blur-1 {
  animation: redBlurFloat 8s ease-in-out infinite;
}

.red-blur-2 {
  animation: redBlurFloat 10s ease-in-out infinite reverse;
}

.red-blur-3 {
  animation: redBlurPulse 6s ease-in-out infinite;
}

.red-blur-4 {
  animation: redBlurFloat 12s ease-in-out infinite;
}

.red-blur-5 {
  animation: redBlurPulse 9s ease-in-out infinite;
}

/* Smoke animation assignments */
.smoke-1 {
  animation: smokeDrift1 18s ease-in-out infinite;
}

.smoke-2 {
  animation: smokeDrift2 22s ease-in-out infinite;
}

.smoke-3 {
  animation: smokeDrift3 14s ease-in-out infinite;
}

.smoke-4 {
  animation: smokeDrift4 25s ease-in-out infinite;
}

.smoke-5 {
  animation: smokeDrift5 20s ease-in-out infinite;
}

.smoke-6 {
  animation: smokeDrift6 12s ease-in-out infinite;
}

.smoke-7 {
  animation: smokeDrift7 16s ease-in-out infinite;
}







/* Code Grid Background */
.code-grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(55, 65, 81, 0.15) 1px, transparent 1px),
    linear-gradient(90deg, rgba(55, 65, 81, 0.15) 1px, transparent 1px);
  background-size: 40px 40px;
  animation: codeGridMove 25s linear infinite;
}

@keyframes codeGridMove {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  50% {
    transform: translate(20px, 20px) rotate(1deg);
  }
  100% {
    transform: translate(40px, 40px) rotate(0deg);
  }
}

/* Binary Particles */
.binary-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.binary-particle {
  position: absolute;
  font-family: 'Courier New', monospace;
  font-weight: bold;
  animation: binaryFloat 18s infinite linear;
  text-shadow: 0 0 5px rgba(59, 130, 246, 0.8);
}

@keyframes binaryFloat {
  0% {
    transform: translateY(100vh) translateX(0px) rotate(0deg);
    opacity: 0;
  }
  5% {
    opacity: 0.8;
  }
  95% {
    opacity: 0.8;
  }
  100% {
    transform: translateY(-100px) translateX(50px) rotate(360deg);
    opacity: 0;
  }
}
</style>
