<template>
  <div class="bg-gray-100 text-gray-800">
    <!-- Sticky Navigation (Hidden initially, shows after hero) -->
    <header
      ref="stickyNav"
      class="fixed top-0 left-0 right-0 z-50 bg-white shadow-lg transform -translate-y-full transition-transform duration-300"
      :class="{ 'translate-y-0': showStickyNav }"
    >
      <div class="max-w-7xl mx-auto flex justify-between items-center p-4">
        <h1 class="text-xl font-bold text-gray-800"></h1>
        <nav class="space-x-6">
          <a href="#hero" class="text-gray-700 hover:text-blue-600 transition-colors">Home</a>
          <a href="#projects" class="text-gray-700 hover:text-blue-600 transition-colors">Projects</a>
          <a href="#skills" class="text-gray-700 hover:text-blue-600 transition-colors">Skills</a>
          <a href="#contact" class="text-gray-700 hover:text-blue-600 transition-colors">Contact</a>
        </nav>
      </div>
    </header>

    <!-- Hero Section - Full Screen -->
    <section id="hero" ref="heroSection" class="relative min-h-screen flex flex-col">

      <!-- Hero Content with 3D Animation Background -->
      <div class="flex-1 flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-slate-900 via-navy-900 to-slate-900">
        <!-- 3D Animated Background -->
        <div class="absolute inset-0 overflow-hidden">
          <!-- Floating programming and music elements -->
          <div class="floating-elements">
            <!-- Code brackets -->
            <div class="element code-element element-1">{ }</div>
            <div class="element code-element element-2">&lt;/&gt;</div>
            <div class="element code-element element-3">( )</div>
            <div class="element code-element element-4">[ ]</div>

            <!-- Music notes -->
            <div class="element music-element element-5">♪</div>
            <div class="element music-element element-6">♫</div>
            <div class="element music-element element-7">♬</div>
            <div class="element music-element element-8">♩</div>

            <!-- Programming symbols -->
            <div class="element symbol-element element-9">=></div>
            <div class="element symbol-element element-10">&&</div>
            <div class="element symbol-element element-11">#</div>
            <div class="element symbol-element element-12">$</div>
          </div>

          <!-- Animated code grid background -->
          <div class="code-grid-background"></div>

          <!-- Binary particle effect -->
          <div class="binary-particles">
            <div class="binary-particle" v-for="n in 30" :key="n" :style="getBinaryParticleStyle(n)">
              {{ Math.random() > 0.5 ? '1' : '0' }}
            </div>
          </div>
        </div>

        <!-- Hero Content -->
        <div class="relative z-10 text-center text-white px-6 max-w-4xl">
          <h2 class="text-5xl md:text-7xl font-bold mb-6 leading-tight">
            Hi, I'm a
            <span class="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-blue-400 to-indigo-400">
              Web Developer
            </span>
          </h2>
          <p class="text-xl md:text-2xl mb-8 text-gray-200 leading-relaxed">
            I create modern and responsive web applications using Laravel, Vue, and Tailwind CSS
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="#projects" class="bg-white/10 backdrop-blur-sm border border-white/20 text-white px-8 py-3 rounded-full font-semibold hover:bg-white/20 transition-all duration-300">
              View My Work
            </a>
            <a href="#contact" class="border-2 border-white/30 text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-navy-900 transition-all duration-300">
              Get In Touch
            </a>
          </div>
        </div>
      </div>

      <!-- Scroll Indicator -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
      </div>
    </section>

    <!-- Main Content -->
    <main class="bg-white">
      <!-- Projects Section -->
      <section id="projects" class="py-20 px-8">
        <div class="max-w-6xl mx-auto">
          <div class="text-center mb-16">
            <h3 class="text-4xl font-bold mb-4 text-gray-800">Featured Projects</h3>
            <p class="text-xl text-gray-600">Some of my recent work and contributions</p>
          </div>
          <div class="grid md:grid-cols-2 gap-8">
            <div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow border border-gray-100">
              <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h4 class="font-bold text-xl mb-3 text-gray-800">Online Student Clearance System</h4>
              <p class="text-gray-600 mb-4">A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p>
              <div class="flex flex-wrap gap-2">
                <span class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full">Laravel</span>
                <span class="px-3 py-1 bg-green-100 text-green-700 text-sm rounded-full">Vue.js</span>
                <span class="px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded-full">MySQL</span>
              </div>
            </div>
            <div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow border border-gray-100">
              <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6">
                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
              <h4 class="font-bold text-xl mb-3 text-gray-800">Intern Tracker Tool</h4>
              <p class="text-gray-600 mb-4">An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p>
              <div class="flex flex-wrap gap-2">
                <span class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full">Laravel</span>
                <span class="px-3 py-1 bg-yellow-100 text-yellow-700 text-sm rounded-full">JavaScript</span>
                <span class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full">Bootstrap</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Skills Section -->
      <section id="skills" class="py-20 px-8 bg-gray-50">
        <div class="max-w-6xl mx-auto">
          <div class="text-center mb-16">
            <h3 class="text-4xl font-bold mb-4 text-gray-800">Skills & Technologies</h3>
            <p class="text-xl text-gray-600">Technologies I work with to bring ideas to life</p>
          </div>
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
            <div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center">
              <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span class="text-red-600 font-bold text-lg">L</span>
              </div>
              <span class="font-semibold text-gray-800">Laravel</span>
            </div>
            <div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center">
              <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span class="text-green-600 font-bold text-lg">V</span>
              </div>
              <span class="font-semibold text-gray-800">Vue.js</span>
            </div>
            <div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center">
              <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span class="text-blue-600 font-bold text-lg">T</span>
              </div>
              <span class="font-semibold text-gray-800">Tailwind CSS</span>
            </div>
            <div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center">
              <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span class="text-yellow-600 font-bold text-lg">U</span>
              </div>
              <span class="font-semibold text-gray-800">UI/UX</span>
            </div>
            <div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center">
              <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span class="text-gray-600 font-bold text-lg">G</span>
              </div>
              <span class="font-semibold text-gray-800">Git</span>
            </div>
          </div>
        </div>
      </section>

      <!-- Contact Section -->
      <section id="contact" class="py-20 px-8 bg-gradient-to-br from-blue-600 to-purple-600">
        <div class="max-w-4xl mx-auto text-center">
          <h3 class="text-4xl font-bold mb-6 text-white">Let's Work Together</h3>
          <p class="text-xl text-blue-100 mb-8">Ready to bring your ideas to life? Let's discuss your next project.</p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="mailto:<EMAIL>" class="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors">
              Send Email
            </a>
            <a href="#" class="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-colors">
              Download Resume
            </a>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'

export default {
  name: 'Portfolio',
  setup() {
    const showStickyNav = ref(false)
    const heroSection = ref(null)
    const stickyNav = ref(null)

    const handleScroll = () => {
      if (heroSection.value) {
        const heroBottom = heroSection.value.offsetTop + heroSection.value.offsetHeight
        const scrollPosition = window.scrollY + 100 // Add some offset

        showStickyNav.value = scrollPosition >= heroBottom
      }
    }

    onMounted(() => {
      window.addEventListener('scroll', handleScroll)
      handleScroll() // Check initial position
    })

    onUnmounted(() => {
      window.removeEventListener('scroll', handleScroll)
    })

    // Generate random styles for particles
    const getParticleStyle = (index) => {
      const size = Math.random() * 4 + 1
      const left = Math.random() * 100
      const animationDelay = Math.random() * 20
      const animationDuration = Math.random() * 10 + 10

      return {
        width: `${size}px`,
        height: `${size}px`,
        left: `${left}%`,
        animationDelay: `${animationDelay}s`,
        animationDuration: `${animationDuration}s`
      }
    }

    // Generate random styles for binary particles
    const getBinaryParticleStyle = (index) => {
      const fontSize = Math.random() * 8 + 12
      const left = Math.random() * 100
      const animationDelay = Math.random() * 15
      const animationDuration = Math.random() * 8 + 12

      return {
        fontSize: `${fontSize}px`,
        left: `${left}%`,
        animationDelay: `${animationDelay}s`,
        animationDuration: `${animationDuration}s`,
        color: `rgba(30, 58, 138, ${Math.random() * 0.5 + 0.3})`
      }
    }

    return {
      showStickyNav,
      heroSection,
      stickyNav,
      getParticleStyle,
      getBinaryParticleStyle
    }
  }
}
</script>

<style scoped>
/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Ensure sections have proper spacing for navigation */
section {
  scroll-margin-top: 80px;
}

/* Navy Blue Color Definitions */
.bg-navy-900 {
  background-color: #1e3a8a;
}

.text-navy-900 {
  color: #1e3a8a;
}

/* 3D Animation Background Styles */
.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.element {
  position: absolute;
  font-weight: bold;
  animation: float3d 15s infinite linear;
  text-shadow: 0 0 10px rgba(30, 58, 138, 0.5);
  user-select: none;
  cursor: pointer;
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* All Elements - Unified Navy Blue */
.code-element,
.music-element,
.symbol-element {
  color: rgba(30, 58, 138, 0.7);
  text-shadow: 0 0 15px rgba(30, 58, 138, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

/* Code Elements */
.code-element {
  font-family: 'Courier New', monospace;
  font-size: 2rem;
}

/* Music Elements */
.music-element {
  font-size: 2.5rem;
}

/* Symbol Elements */
.symbol-element {
  font-family: 'Courier New', monospace;
  font-size: 1.8rem;
}

/* Individual Element Hover Effects - Lighter Navy Blue */
.code-element:hover,
.music-element:hover,
.symbol-element:hover {
  color: rgba(96, 165, 250, 0.9) !important;
  text-shadow: 0 0 25px rgba(96, 165, 250, 0.8) !important;
  transform: scale(1.15) !important;
  animation-play-state: paused;
  filter: brightness(1.2);
}

.element-1 {
  top: 15%;
  left: 10%;
  animation-delay: 0s;
}

.element-2 {
  top: 25%;
  left: 85%;
  animation-delay: -2s;
}

.element-3 {
  top: 45%;
  left: 15%;
  animation-delay: -4s;
}

.element-4 {
  top: 65%;
  left: 80%;
  animation-delay: -6s;
}

.element-5 {
  top: 20%;
  left: 60%;
  animation-delay: -1s;
}

.element-6 {
  top: 75%;
  left: 25%;
  animation-delay: -3s;
}

.element-7 {
  top: 35%;
  left: 75%;
  animation-delay: -5s;
}

.element-8 {
  top: 85%;
  left: 50%;
  animation-delay: -7s;
}

.element-9 {
  top: 10%;
  left: 40%;
  animation-delay: -1.5s;
}

.element-10 {
  top: 55%;
  left: 5%;
  animation-delay: -3.5s;
}

.element-11 {
  top: 30%;
  left: 90%;
  animation-delay: -5.5s;
}

.element-12 {
  top: 80%;
  left: 70%;
  animation-delay: -7.5s;
}

@keyframes float3d {
  0% {
    transform: translateY(0px) translateX(0px) rotateX(0deg) rotateY(0deg) rotateZ(0deg) scale(1);
  }
  25% {
    transform: translateY(-30px) translateX(20px) rotateX(90deg) rotateY(180deg) rotateZ(90deg) scale(1.1);
  }
  50% {
    transform: translateY(-10px) translateX(-15px) rotateX(180deg) rotateY(360deg) rotateZ(180deg) scale(0.9);
  }
  75% {
    transform: translateY(-25px) translateX(10px) rotateX(270deg) rotateY(180deg) rotateZ(270deg) scale(1.05);
  }
  100% {
    transform: translateY(0px) translateX(0px) rotateX(360deg) rotateY(360deg) rotateZ(360deg) scale(1);
  }
}

/* Code Grid Background */
.code-grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(30, 58, 138, 0.15) 1px, transparent 1px),
    linear-gradient(90deg, rgba(30, 58, 138, 0.15) 1px, transparent 1px);
  background-size: 40px 40px;
  animation: codeGridMove 25s linear infinite;
}

@keyframes codeGridMove {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  50% {
    transform: translate(20px, 20px) rotate(1deg);
  }
  100% {
    transform: translate(40px, 40px) rotate(0deg);
  }
}

/* Binary Particles */
.binary-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.binary-particle {
  position: absolute;
  font-family: 'Courier New', monospace;
  font-weight: bold;
  animation: binaryFloat 18s infinite linear;
  text-shadow: 0 0 5px rgba(59, 130, 246, 0.8);
}

@keyframes binaryFloat {
  0% {
    transform: translateY(100vh) translateX(0px) rotate(0deg);
    opacity: 0;
  }
  5% {
    opacity: 0.8;
  }
  95% {
    opacity: 0.8;
  }
  100% {
    transform: translateY(-100px) translateX(50px) rotate(360deg);
    opacity: 0;
  }
}
</style>
