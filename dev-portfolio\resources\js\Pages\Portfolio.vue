<template>
  <div style="background-color: #0a0a0a; color: #f8fafc;">
    <!-- Sticky Navigation (Hidden initially, shows after hero) -->
    <header
      ref="stickyNav"
      class="fixed top-0 left-0 right-0 z-50 shadow-lg transform -translate-y-full transition-transform duration-300"
      style="background-color: #1a1a1a;"
      :class="{ 'translate-y-0': showStickyNav }"
    >
      <div class="max-w-7xl mx-auto flex justify-center items-center p-4">
        <nav class="space-x-8">
          <button @click="scrollToSection('hero')" class="transition-colors" style="color: #cbd5e1;" onmouseover="this.style.color='#dc2626';" onmouseout="this.style.color='#cbd5e1';">Home</button>
          <button @click="scrollToSection('projects')" class="transition-colors" style="color: #cbd5e1;" onmouseover="this.style.color='#dc2626';" onmouseout="this.style.color='#cbd5e1';">Projects</button>
          <button @click="scrollToSection('skills')" class="transition-colors" style="color: #cbd5e1;" onmouseover="this.style.color='#dc2626';" onmouseout="this.style.color='#cbd5e1';">Skills</button>
          <button @click="scrollToSection('contact')" class="transition-colors" style="color: #cbd5e1;" onmouseover="this.style.color='#dc2626';" onmouseout="this.style.color='#cbd5e1';">Contact</button>
        </nav>
      </div>
    </header>

    <!-- Hero Section - Full Screen -->
    <section id="hero" ref="heroSection" class="relative min-h-screen flex flex-col">

      <!-- Hero Content with 3D Animation Background -->
      <div class="flex-1 flex items-center justify-center relative overflow-hidden" style="background: linear-gradient(135deg, #111827 0%, #0a0a0a 100%)">
        <!-- Space Particles Background -->
        <div class="absolute inset-0 overflow-hidden">
          <!-- Starfield Background -->
          <div class="starfield">
            <div class="star" v-for="n in 100" :key="'star-' + n" :style="getStarStyle(n)"></div>
          </div>

          <!-- Floating space programming and music particles -->
          <div class="space-particles">
            <!-- Programming particles -->
            <div class="particle code-particle particle-1">{ }</div>
            <div class="particle code-particle particle-2">&lt;/&gt;</div>
            <div class="particle code-particle particle-3">( )</div>
            <div class="particle code-particle particle-4">[ ]</div>
            <div class="particle code-particle particle-5">=></div>
            <div class="particle code-particle particle-6">&&</div>
            <div class="particle code-particle particle-7">#</div>
            <div class="particle code-particle particle-8">$</div>

            <!-- Music particles -->
            <div class="particle music-particle particle-9">♪</div>
            <div class="particle music-particle particle-10">♫</div>
            <div class="particle music-particle particle-11">♬</div>
            <div class="particle music-particle particle-12">♩</div>
            <div class="particle music-particle particle-13">♭</div>
            <div class="particle music-particle particle-14">♯</div>
            <div class="particle music-particle particle-15">𝄞</div>
            <div class="particle music-particle particle-16">𝄢</div>
          </div>

          <!-- Cosmic dust effect -->
          <div class="cosmic-dust">
            <div class="dust-particle" v-for="n in 50" :key="'dust-' + n" :style="getDustParticleStyle(n)"></div>
          </div>

          <!-- Nebula glow effect -->
          <div class="nebula-glow"></div>
        </div>

        <!-- Hero Content -->
        <div class="relative z-10 text-center px-6 max-w-4xl" style="color: #f8fafc;">
          <h2 class="text-5xl md:text-7xl font-bold mb-6 leading-tight">
            Hello! I am
            <span class="text-transparent bg-clip-text" style="background: linear-gradient(45deg, #dc2626, #fca5a5); -webkit-background-clip: text; background-clip: text;">
              Isaac Martel.
            </span>
          </h2>
          <p class="text-xl md:text-2xl mb-8 leading-relaxed" style="color: #cbd5e1;">
            I am a software engineer and full-stack web developer.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button @click="scrollToSection('projects')" class="px-8 py-3 rounded-full font-semibold transition-all duration-300" style="background-color: #dc2626; color: #f8fafc; border: 2px solid #dc2626;" onmouseover="this.style.backgroundColor='#991b1b'; this.style.borderColor='#991b1b';" onmouseout="this.style.backgroundColor='#dc2626'; this.style.borderColor='#dc2626';">
              View My Work
            </button>
            <button @click="scrollToSection('contact')" class="px-8 py-3 rounded-full font-semibold transition-all duration-300" style="background-color: transparent; color: #f8fafc; border: 2px solid #374151;" onmouseover="this.style.backgroundColor='#374151'; this.style.borderColor='#374151';" onmouseout="this.style.backgroundColor='transparent'; this.style.borderColor='#374151';">
              Get In Touch
            </button>
          </div>
        </div>
      </div>

      <!-- Scroll Indicator -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
        </svg>
      </div>
    </section>

    <!-- Main Content -->
    <main style="background-color: #0a0a0a;">
      <!-- Projects Section -->
      <section id="projects" class="py-20 px-8">
        <div class="max-w-6xl mx-auto">
          <div class="text-center mb-16">
            <h3 class="text-4xl font-bold mb-4" style="color: #f8fafc;">Featured Projects</h3>
            <p class="text-xl" style="color: #cbd5e1;">Some of my recent work and contributions</p>
          </div>
          <div class="grid md:grid-cols-2 gap-8">
            <div class="p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow" style="background-color: #1a1a1a; border: 2px solid #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mb-6" style="background-color: #dc2626;">
                <svg class="w-6 h-6" style="color: #f8fafc;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h4 class="font-bold text-xl mb-3" style="color: #f8fafc;">Online Student Clearance System</h4>
              <p class="mb-4" style="color: #cbd5e1;">A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p>
              <div class="flex flex-wrap gap-2">
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #dc2626; color: #f8fafc;">Laravel</span>
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #374151; color: #f8fafc;">Vue.js</span>
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #64748b; color: #f8fafc;">MySQL</span>
              </div>
            </div>
            <div class="p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow" style="background-color: #1a1a1a; border: 2px solid #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mb-6" style="background-color: #374151;">
                <svg class="w-6 h-6" style="color: #f8fafc;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
              <h4 class="font-bold text-xl mb-3" style="color: #f8fafc;">Intern Tracker Tool</h4>
              <p class="mb-4" style="color: #cbd5e1;">An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p>
              <div class="flex flex-wrap gap-2">
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #dc2626; color: #f8fafc;">Laravel</span>
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #374151; color: #f8fafc;">JavaScript</span>
                <span class="px-3 py-1 text-sm rounded-full" style="background-color: #64748b; color: #f8fafc;">Bootstrap</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Skills Section -->
      <section id="skills" class="py-20 px-8" style="background-color: #1a1a1a;">
        <div class="max-w-6xl mx-auto">
          <div class="text-center mb-16">
            <h3 class="text-4xl font-bold mb-4" style="color: #f8fafc;">Skills & Technologies</h3>
            <p class="text-xl" style="color: #cbd5e1;">Technologies I work with to bring ideas to life</p>
          </div>
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
            <div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color: #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color: #dc2626;">
                <span class="font-bold text-lg" style="color: #f8fafc;">L</span>
              </div>
              <span class="font-semibold" style="color: #f8fafc;">Laravel</span>
            </div>
            <div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color: #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color: #374151;">
                <span class="font-bold text-lg" style="color: #f8fafc;">V</span>
              </div>
              <span class="font-semibold" style="color: #f8fafc;">Vue.js</span>
            </div>
            <div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color: #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color: #64748b;">
                <span class="font-bold text-lg" style="color: #f8fafc;">T</span>
              </div>
              <span class="font-semibold" style="color: #f8fafc;">Tailwind CSS</span>
            </div>
            <div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color: #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color: #dc2626;">
                <span class="font-bold text-lg" style="color: #f8fafc;">U</span>
              </div>
              <span class="font-semibold" style="color: #f8fafc;">UI/UX</span>
            </div>
            <div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color: #2a2a2a;">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color: #374151;">
                <span class="font-bold text-lg" style="color: #f8fafc;">G</span>
              </div>
              <span class="font-semibold" style="color: #f8fafc;">Git</span>
            </div>
          </div>
        </div>
      </section>

      <!-- Contact Section -->
      <section id="contact" class="py-20 px-8" style="background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);">
        <div class="max-w-4xl mx-auto text-center">
          <h3 class="text-4xl font-bold mb-6" style="color: #f8fafc;">Let's Work Together</h3>
          <p class="text-xl mb-8" style="color: #fca5a5;">Ready to bring your ideas to life? Let's discuss your next project.</p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="mailto:<EMAIL>" class="px-8 py-3 rounded-full font-semibold transition-colors" style="background-color: #f8fafc; color: #dc2626;" onmouseover="this.style.backgroundColor='#cbd5e1';" onmouseout="this.style.backgroundColor='#f8fafc';">
              Send Email
            </a>
            <a href="#" class="px-8 py-3 rounded-full font-semibold transition-colors" style="border: 2px solid #f8fafc; color: #f8fafc; background-color: transparent;" onmouseover="this.style.backgroundColor='#f8fafc'; this.style.color='#dc2626';" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#f8fafc';">
              Download Resume
            </a>
          </div>
        </div>
      </section>
    </main>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'

export default {
  name: 'Portfolio',
  setup() {
    const showStickyNav = ref(false)
    const heroSection = ref(null)
    const stickyNav = ref(null)

    const handleScroll = () => {
      if (heroSection.value) {
        const heroBottom = heroSection.value.offsetTop + heroSection.value.offsetHeight
        const scrollPosition = window.scrollY + 100 // Add some offset

        showStickyNav.value = scrollPosition >= heroBottom
      }
    }

    onMounted(() => {
      window.addEventListener('scroll', handleScroll)
      handleScroll() // Check initial position
    })

    onUnmounted(() => {
      window.removeEventListener('scroll', handleScroll)
    })

    // Generate random styles for particles
    const getParticleStyle = (index) => {
      const size = Math.random() * 4 + 1
      const left = Math.random() * 100
      const animationDelay = Math.random() * 20
      const animationDuration = Math.random() * 10 + 10

      return {
        width: `${size}px`,
        height: `${size}px`,
        left: `${left}%`,
        animationDelay: `${animationDelay}s`,
        animationDuration: `${animationDuration}s`
      }
    }

    // Generate random styles for binary particles
    const getBinaryParticleStyle = (index) => {
      const fontSize = Math.random() * 8 + 12
      const left = Math.random() * 100
      const animationDelay = Math.random() * 15
      const animationDuration = Math.random() * 8 + 12

      return {
        fontSize: `${fontSize}px`,
        left: `${left}%`,
        animationDelay: `${animationDelay}s`,
        animationDuration: `${animationDuration}s`,
        color: `rgba(55, 65, 81, ${Math.random() * 0.5 + 0.3})`
      }
    }

    // Generate random styles for stars
    const getStarStyle = (index) => {
      const size = Math.random() * 3 + 1
      const left = Math.random() * 100
      const top = Math.random() * 100
      const animationDelay = Math.random() * 10
      const animationDuration = Math.random() * 5 + 3
      const opacity = Math.random() * 0.8 + 0.2

      return {
        width: `${size}px`,
        height: `${size}px`,
        left: `${left}%`,
        top: `${top}%`,
        animationDelay: `${animationDelay}s`,
        animationDuration: `${animationDuration}s`,
        opacity: opacity
      }
    }

    // Generate random styles for cosmic dust particles
    const getDustParticleStyle = (index) => {
      const size = Math.random() * 2 + 0.5
      const left = Math.random() * 100
      const animationDelay = Math.random() * 20
      const animationDuration = Math.random() * 15 + 10

      return {
        width: `${size}px`,
        height: `${size}px`,
        left: `${left}%`,
        animationDelay: `${animationDelay}s`,
        animationDuration: `${animationDuration}s`
      }
    }

    // Create constellation connections
    const createConstellationConnections = () => {
      const particles = document.querySelectorAll('.particle')
      const svg = document.querySelector('.connection-lines')

      if (!svg || particles.length === 0) return

      // Clear existing lines
      svg.innerHTML = ''

      const particlePositions = Array.from(particles).map(particle => {
        const rect = particle.getBoundingClientRect()
        const containerRect = particle.closest('.space-particles').getBoundingClientRect()
        return {
          x: ((rect.left + rect.width / 2 - containerRect.left) / containerRect.width) * 100,
          y: ((rect.top + rect.height / 2 - containerRect.top) / containerRect.height) * 100,
          element: particle
        }
      })

      // Create connections between nearby particles
      for (let i = 0; i < particlePositions.length; i++) {
        for (let j = i + 1; j < particlePositions.length; j++) {
          const p1 = particlePositions[i]
          const p2 = particlePositions[j]
          const distance = Math.sqrt((p1.x - p2.x) ** 2 + (p1.y - p2.y) ** 2)

          // Create constellation-like connections
          if (distance < 25) {
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line')
            line.setAttribute('x1', `${p1.x}%`)
            line.setAttribute('y1', `${p1.y}%`)
            line.setAttribute('x2', `${p2.x}%`)
            line.setAttribute('y2', `${p2.y}%`)
            line.setAttribute('stroke', 'rgba(248, 250, 252, 0.3)')
            line.setAttribute('stroke-width', '1')
            line.setAttribute('opacity', Math.max(0.2, 1 - distance / 25))

            // Add hover effects
            const addHoverEffect = () => {
              line.setAttribute('stroke', 'rgba(220, 38, 38, 0.7)')
              line.setAttribute('stroke-width', '2')
              line.setAttribute('opacity', '0.9')
              line.classList.add('highlighted')
            }

            const removeHoverEffect = () => {
              line.setAttribute('stroke', 'rgba(248, 250, 252, 0.3)')
              line.setAttribute('stroke-width', '1')
              line.setAttribute('opacity', Math.max(0.2, 1 - distance / 25))
              line.classList.remove('highlighted')
            }

            p1.element.addEventListener('mouseenter', addHoverEffect)
            p2.element.addEventListener('mouseenter', addHoverEffect)
            p1.element.addEventListener('mouseleave', removeHoverEffect)
            p2.element.addEventListener('mouseleave', removeHoverEffect)

            svg.appendChild(line)
          }
        }
      }
    }

    // Smooth scroll to section function
    const scrollToSection = (sectionId) => {
      const element = document.getElementById(sectionId)
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        })
      }
    }

    // Initialize constellation connections after mount
    onMounted(() => {
      setTimeout(() => {
        createConstellationConnections()
        // Recreate connections periodically to account for animation
        setInterval(createConstellationConnections, 5000)
      }, 1000)
    })

    return {
      showStickyNav,
      heroSection,
      stickyNav,
      getParticleStyle,
      getBinaryParticleStyle,
      getStarStyle,
      getDustParticleStyle,
      scrollToSection
    }
  }
}
</script>

<style scoped>
/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Ensure sections have proper spacing for navigation */
section {
  scroll-margin-top: 80px;
}

/* Navy Blue Color Definitions */
.bg-navy-900 {
  background-color: #1e3a8a;
}

.text-navy-900 {
  color: #1e3a8a;
}

/* Space Particles Background Styles */
.starfield {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.star {
  position: absolute;
  background: #f8fafc;
  border-radius: 50%;
  animation: twinkle 4s infinite ease-in-out;
  box-shadow: 0 0 6px rgba(248, 250, 252, 0.8);
}

.space-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.space-particles .particle {
  pointer-events: auto;
}

.particle {
  position: absolute;
  font-weight: bold;
  animation: spaceFloat 20s infinite linear;
  user-select: none;
  cursor: pointer;
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
  filter: drop-shadow(0 0 8px currentColor);
  z-index: 10;
  padding: 8px;
  border-radius: 4px;
}

/* Programming Particles */
.code-particle {
  font-family: 'Courier New', monospace;
  font-size: 1.5rem;
  color: rgba(55, 65, 81, 0.7);
  text-shadow: 0 0 12px rgba(55, 65, 81, 0.5);
  transition: all 0.3s ease;
}

/* Music Particles */
.music-particle {
  font-size: 1.8rem;
  color: rgba(55, 65, 81, 0.7);
  text-shadow: 0 0 12px rgba(55, 65, 81, 0.5);
  transition: all 0.3s ease;
}

/* Individual Particle Hover Effects - Red Color */
.particle:hover,
.code-particle:hover,
.music-particle:hover {
  color: #dc2626 !important;
  text-shadow: 0 0 25px rgba(220, 38, 38, 1) !important;
  transform: scale(1.2) !important;
  animation-play-state: paused !important;
  filter: brightness(1.5) drop-shadow(0 0 15px #dc2626) !important;
  background-color: rgba(220, 38, 38, 0.1) !important;
  z-index: 20 !important;
}

/* Constellation Connection Lines */
.connection-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 5;
}

.connection-lines line {
  stroke: rgba(248, 250, 252, 0.3);
  stroke-width: 1;
  opacity: 0.5;
  animation: constellationGlow 4s ease-in-out infinite alternate;
  filter: drop-shadow(0 0 2px rgba(248, 250, 252, 0.2));
}

.connection-lines line.highlighted {
  stroke: rgba(220, 38, 38, 0.7);
  stroke-width: 2;
  opacity: 0.9;
  animation: constellationPulse 2s ease-in-out infinite;
}

/* Cosmic Dust */
.cosmic-dust {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.dust-particle {
  position: absolute;
  background: rgba(248, 250, 252, 0.3);
  border-radius: 50%;
  animation: cosmicDrift 25s infinite linear;
  box-shadow: 0 0 4px rgba(248, 250, 252, 0.2);
}

/* Nebula Glow Effect */
.nebula-glow {
  position: absolute;
  top: 20%;
  left: 10%;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(220, 38, 38, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  animation: nebulaFloat 30s infinite ease-in-out;
  filter: blur(40px);
}

/* Particle Positioning */
.particle-1 { top: 10%; left: 15%; animation-delay: 0s; }
.particle-2 { top: 20%; left: 85%; animation-delay: -2s; }
.particle-3 { top: 35%; left: 25%; animation-delay: -4s; }
.particle-4 { top: 50%; left: 75%; animation-delay: -6s; }
.particle-5 { top: 65%; left: 10%; animation-delay: -8s; }
.particle-6 { top: 80%; left: 60%; animation-delay: -10s; }
.particle-7 { top: 15%; left: 45%; animation-delay: -12s; }
.particle-8 { top: 90%; left: 30%; animation-delay: -14s; }
.particle-9 { top: 25%; left: 70%; animation-delay: -1s; }
.particle-10 { top: 40%; left: 90%; animation-delay: -3s; }
.particle-11 { top: 55%; left: 5%; animation-delay: -5s; }
.particle-12 { top: 70%; left: 50%; animation-delay: -7s; }
.particle-13 { top: 85%; left: 80%; animation-delay: -9s; }
.particle-14 { top: 5%; left: 65%; animation-delay: -11s; }
.particle-15 { top: 45%; left: 35%; animation-delay: -13s; }
.particle-16 { top: 75%; left: 20%; animation-delay: -15s; }

/* Space Animation Keyframes */
@keyframes spaceFloat {
  0% {
    transform: translateY(0px) translateX(0px) rotateZ(0deg) scale(1);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-40px) translateX(30px) rotateZ(90deg) scale(1.1);
    opacity: 1;
  }
  50% {
    transform: translateY(-20px) translateX(-25px) rotateZ(180deg) scale(0.9);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-35px) translateX(15px) rotateZ(270deg) scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: translateY(0px) translateX(0px) rotateZ(360deg) scale(1);
    opacity: 0.7;
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes cosmicDrift {
  0% {
    transform: translateY(100vh) translateX(0px) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(-100px) translateX(100px) rotate(360deg);
    opacity: 0;
  }
}

@keyframes nebulaFloat {
  0%, 100% {
    transform: translateX(0px) translateY(0px) scale(1);
    opacity: 0.3;
  }
  33% {
    transform: translateX(50px) translateY(-30px) scale(1.1);
    opacity: 0.5;
  }
  66% {
    transform: translateX(-30px) translateY(40px) scale(0.9);
    opacity: 0.4;
  }
}

/* Code Grid Background */
.code-grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(55, 65, 81, 0.15) 1px, transparent 1px),
    linear-gradient(90deg, rgba(55, 65, 81, 0.15) 1px, transparent 1px);
  background-size: 40px 40px;
  animation: codeGridMove 25s linear infinite;
}

@keyframes codeGridMove {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  50% {
    transform: translate(20px, 20px) rotate(1deg);
  }
  100% {
    transform: translate(40px, 40px) rotate(0deg);
  }
}

/* Binary Particles */
.binary-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.binary-particle {
  position: absolute;
  font-family: 'Courier New', monospace;
  font-weight: bold;
  animation: binaryFloat 18s infinite linear;
  text-shadow: 0 0 5px rgba(59, 130, 246, 0.8);
}

@keyframes binaryFloat {
  0% {
    transform: translateY(100vh) translateX(0px) rotate(0deg);
    opacity: 0;
  }
  5% {
    opacity: 0.8;
  }
  95% {
    opacity: 0.8;
  }
  100% {
    transform: translateY(-100px) translateX(50px) rotate(360deg);
    opacity: 0;
  }
}

/* Constellation Animation Keyframes */
@keyframes constellationGlow {
  0% {
    opacity: 0.3;
    stroke-width: 1;
  }
  100% {
    opacity: 0.7;
    stroke-width: 1.5;
  }
}

@keyframes constellationPulse {
  0%, 100% {
    opacity: 0.7;
    stroke-width: 2;
  }
  50% {
    opacity: 1;
    stroke-width: 3;
  }
}
</style>
