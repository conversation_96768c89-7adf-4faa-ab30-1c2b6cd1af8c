<template>
  <div class="min-h-screen bg-gray-100 text-gray-800">
    <header class="bg-white shadow p-6">
      <div class="max-w-7xl mx-auto flex justify-between items-center">
        <h1 class="text-2xl font-bold"><PERSON></h1>
        <nav class="space-x-4">
          <a href="#projects" class="hover:text-blue-500">Projects</a>
          <a href="#skills" class="hover:text-blue-500">Skills</a>
          <a href="#contact" class="hover:text-blue-500">Contact</a>
        </nav>
      </div>
    </header>

    <main class="p-8 max-w-5xl mx-auto">
      <!-- Hero Section -->
      <section class="text-center my-12">
        <h2 class="text-4xl font-bold mb-4">Hi, I’m a Web Developer</h2>
        <p class="text-gray-600">I create modern and responsive web applications using Lara<PERSON>, Vue, and Tailwind.</p>
      </section>

      <!-- Projects Section -->
      <section id="projects" class="my-16">
        <h3 class="text-2xl font-semibold mb-6">Projects</h3>
        <div class="grid md:grid-cols-2 gap-6">
          <div class="bg-white p-6 rounded-xl shadow">
            <h4 class="font-bold text-lg mb-2">Online Student Clearance System</h4>
            <p class="text-gray-600">A web app for Xavier University designed for online student clearance submission and processing.</p>
          </div>
          <div class="bg-white p-6 rounded-xl shadow">
            <h4 class="font-bold text-lg mb-2">Intern Tracker Tool</h4>
            <p class="text-gray-600">An internal tool for managing intern reports, attendance, and onboarding processes.</p>
          </div>
        </div>
      </section>

      <!-- Skills Section -->
      <section id="skills" class="my-16">
        <h3 class="text-2xl font-semibold mb-6">Skills</h3>
        <div class="flex flex-wrap gap-4">
          <span class="px-4 py-2 bg-blue-100 text-blue-700 rounded-full">Laravel</span>
          <span class="px-4 py-2 bg-green-100 text-green-700 rounded-full">Vue.js</span>
          <span class="px-4 py-2 bg-purple-100 text-purple-700 rounded-full">Tailwind CSS</span>
          <span class="px-4 py-2 bg-yellow-100 text-yellow-700 rounded-full">UI/UX</span>
          <span class="px-4 py-2 bg-gray-200 text-gray-800 rounded-full">Git</span>
        </div>
      </section>

      <!-- Contact Section -->
      <section id="contact" class="my-16 text-center">
        <h3 class="text-2xl font-semibold mb-6">Contact</h3>
        <p>Reach me via email at <a href="mailto:<EMAIL>" class="text-blue-500 underline"><EMAIL></a></p>
      </section>
    </main>
  </div>
</template>

<script>
export default {
  name: 'Portfolio',
}
</script>

<style scoped>
/* Additional custom styles (if needed) */
</style>
