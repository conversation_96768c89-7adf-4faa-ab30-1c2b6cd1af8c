import{_ as A}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{f as u,o as v,b as t,n as L,k as b,F as M,l as C,e as _,m as y,p as $,q as z,s as j}from"./app-CSla02eE.js";const B={name:"Portfolio",setup(){const p=y(!1),o=y(null),x=y(null),c=()=>{if(o.value){const i=o.value.offsetTop+o.value.offsetHeight,e=window.scrollY+100;p.value=e>=i}};$(()=>{window.addEventListener("scroll",c),c()}),z(()=>{window.removeEventListener("scroll",c)});const h=i=>{const e=Math.random()*4+1,d=Math.random()*100,a=Math.random()*20,s=Math.random()*10+10;return{width:`${e}px`,height:`${e}px`,left:`${d}%`,animationDelay:`${a}s`,animationDuration:`${s}s`}},g=i=>{const e=Math.random()*8+12,d=Math.random()*100,a=Math.random()*15,s=Math.random()*8+12;return{fontSize:`${e}px`,left:`${d}%`,animationDelay:`${a}s`,animationDuration:`${s}s`,color:`rgba(55, 65, 81, ${Math.random()*.5+.3})`}},n=i=>{const e=Math.random()*3+1,d=Math.random()*100,a=Math.random()*100,s=Math.random()*10,r=Math.random()*5+3,f=Math.random()*.8+.2;return{width:`${e}px`,height:`${e}px`,left:`${d}%`,top:`${a}%`,animationDelay:`${s}s`,animationDuration:`${r}s`,opacity:f}},D=i=>{const e=Math.random()*2+.5,d=Math.random()*100,a=Math.random()*20,s=Math.random()*15+10;return{width:`${e}px`,height:`${e}px`,left:`${d}%`,animationDelay:`${a}s`,animationDuration:`${s}s`}},k=()=>{const i=document.querySelectorAll(".particle"),e=document.querySelector(".connection-lines");if(!e||i.length===0)return;e.innerHTML="";const d=Array.from(i).map(a=>{const s=a.getBoundingClientRect(),r=a.closest(".space-particles").getBoundingClientRect();return{x:(s.left+s.width/2-r.left)/r.width*100,y:(s.top+s.height/2-r.top)/r.height*100,element:a}});for(let a=0;a<d.length;a++)for(let s=a+1;s<d.length;s++){const r=d[a],f=d[s],m=Math.sqrt((r.x-f.x)**2+(r.y-f.y)**2);if(m<25){const l=document.createElementNS("http://www.w3.org/2000/svg","line");l.setAttribute("x1",`${r.x}%`),l.setAttribute("y1",`${r.y}%`),l.setAttribute("x2",`${f.x}%`),l.setAttribute("y2",`${f.y}%`),l.setAttribute("stroke","rgba(248, 250, 252, 0.3)"),l.setAttribute("stroke-width","1"),l.setAttribute("opacity",Math.max(.2,1-m/25));const w=()=>{l.setAttribute("stroke","rgba(220, 38, 38, 0.7)"),l.setAttribute("stroke-width","2"),l.setAttribute("opacity","0.9"),l.classList.add("highlighted")},S=()=>{l.setAttribute("stroke","rgba(248, 250, 252, 0.3)"),l.setAttribute("stroke-width","1"),l.setAttribute("opacity",Math.max(.2,1-m/25)),l.classList.remove("highlighted")};r.element.addEventListener("mouseenter",w),f.element.addEventListener("mouseenter",w),r.element.addEventListener("mouseleave",S),f.element.addEventListener("mouseleave",S),e.appendChild(l)}}},T=i=>{const e=document.getElementById(i);e&&e.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"})};return $(()=>{setTimeout(()=>{k(),setInterval(k,5e3)},1e3)}),{showStickyNav:p,heroSection:o,stickyNav:x,getParticleStyle:h,getBinaryParticleStyle:g,getStarStyle:n,getDustParticleStyle:D,scrollToSection:T}}},E={style:{"background-color":"#0a0a0a",color:"#f8fafc"}},I={class:"max-w-7xl mx-auto flex justify-center items-center p-4"},P={class:"space-x-8"},V={id:"hero",ref:"heroSection",class:"relative min-h-screen flex flex-col"},N={class:"flex-1 flex items-center justify-center relative overflow-hidden",style:{background:"linear-gradient(135deg, #111827 0%, #0a0a0a 100%)"}},H={class:"absolute inset-0 overflow-hidden"},R={class:"starfield"},U={class:"cosmic-dust"},q={class:"relative z-10 text-center px-6 max-w-4xl",style:{color:"#f8fafc"}},F={class:"flex flex-col sm:flex-row gap-4 justify-center"},G={style:{"background-color":"#0a0a0a"}},W={id:"projects",class:"py-20 px-8"},X={class:"max-w-6xl mx-auto"},J={class:"grid md:grid-cols-2 gap-8"},O={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"#1a1a1a",border:"2px solid #2a2a2a"}},Q={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#dc2626"}},Y={class:"w-6 h-6",style:{color:"#f8fafc"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},K={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"#1a1a1a",border:"2px solid #2a2a2a"}},Z={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#374151"}},tt={class:"w-6 h-6",style:{color:"#f8fafc"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"};function ot(p,o,x,c,h,g){return v(),u("div",E,[t("header",{ref:"stickyNav",class:L(["fixed top-0 left-0 right-0 z-50 shadow-lg transform -translate-y-full transition-transform duration-300",{"translate-y-0":c.showStickyNav}]),style:{"background-color":"#1a1a1a"}},[t("div",I,[t("nav",P,[t("button",{onClick:o[0]||(o[0]=n=>c.scrollToSection("hero")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Home"),t("button",{onClick:o[1]||(o[1]=n=>c.scrollToSection("projects")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Projects"),t("button",{onClick:o[2]||(o[2]=n=>c.scrollToSection("skills")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Skills"),t("button",{onClick:o[3]||(o[3]=n=>c.scrollToSection("contact")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Contact")])])],2),t("section",V,[t("div",N,[t("div",H,[t("div",R,[(v(),u(M,null,C(100,n=>t("div",{class:"star",key:"star-"+n,style:j(c.getStarStyle(n))},null,4)),64))]),o[6]||(o[6]=b('<div class="space-particles" data-v-72139cbd><svg class="connection-lines" width="100%" height="100%" data-v-72139cbd></svg><div class="particle code-particle particle-1" data-v-72139cbd>{ }</div><div class="particle code-particle particle-2" data-v-72139cbd>&lt;/&gt;</div><div class="particle code-particle particle-3" data-v-72139cbd>( )</div><div class="particle code-particle particle-4" data-v-72139cbd>[ ]</div><div class="particle code-particle particle-5" data-v-72139cbd>=&gt;</div><div class="particle code-particle particle-6" data-v-72139cbd>&amp;&amp;</div><div class="particle code-particle particle-7" data-v-72139cbd>#</div><div class="particle code-particle particle-8" data-v-72139cbd>$</div><div class="particle music-particle particle-9" data-v-72139cbd>♪</div><div class="particle music-particle particle-10" data-v-72139cbd>♫</div><div class="particle music-particle particle-11" data-v-72139cbd>♬</div><div class="particle music-particle particle-12" data-v-72139cbd>♩</div><div class="particle music-particle particle-13" data-v-72139cbd>♭</div><div class="particle music-particle particle-14" data-v-72139cbd>♯</div><div class="particle music-particle particle-15" data-v-72139cbd>𝄞</div><div class="particle music-particle particle-16" data-v-72139cbd>𝄢</div></div>',1)),t("div",U,[(v(),u(M,null,C(50,n=>t("div",{class:"dust-particle",key:"dust-"+n,style:j(c.getDustParticleStyle(n))},null,4)),64))]),o[7]||(o[7]=t("div",{class:"nebula-glow"},null,-1))]),t("div",q,[o[8]||(o[8]=t("h2",{class:"text-5xl md:text-7xl font-bold mb-6 leading-tight"},[_(" Hello! I am "),t("span",{class:"text-transparent bg-clip-text",style:{background:"linear-gradient(45deg, #dc2626, #fca5a5)","-webkit-background-clip":"text","background-clip":"text"}}," Isaac Martel. ")],-1)),o[9]||(o[9]=t("p",{class:"text-xl md:text-2xl mb-8 leading-relaxed",style:{color:"#cbd5e1"}}," I am a software engineer and full-stack web developer. ",-1)),t("div",F,[t("button",{onClick:o[4]||(o[4]=n=>c.scrollToSection("projects")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300",style:{"background-color":"#dc2626",color:"#f8fafc",border:"2px solid #dc2626"},onmouseover:"this.style.backgroundColor='#991b1b'; this.style.borderColor='#991b1b';",onmouseout:"this.style.backgroundColor='#dc2626'; this.style.borderColor='#dc2626';"}," View My Work "),t("button",{onClick:o[5]||(o[5]=n=>c.scrollToSection("contact")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300",style:{"background-color":"transparent",color:"#f8fafc",border:"2px solid #374151"},onmouseover:"this.style.backgroundColor='#374151'; this.style.borderColor='#374151';",onmouseout:"this.style.backgroundColor='transparent'; this.style.borderColor='#374151';"}," Get In Touch ")])])]),o[10]||(o[10]=t("div",{class:"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce"},[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))],512),t("main",G,[t("section",W,[t("div",X,[o[15]||(o[15]=t("div",{class:"text-center mb-16"},[t("h3",{class:"text-4xl font-bold mb-4",style:{color:"#f8fafc"}},"Featured Projects"),t("p",{class:"text-xl",style:{color:"#cbd5e1"}},"Some of my recent work and contributions")],-1)),t("div",J,[t("div",O,[t("div",Q,[(v(),u("svg",Y,o[11]||(o[11]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)])))]),o[12]||(o[12]=b('<h4 class="font-bold text-xl mb-3" style="color:#f8fafc;" data-v-72139cbd>Online Student Clearance System</h4><p class="mb-4" style="color:#cbd5e1;" data-v-72139cbd>A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p><div class="flex flex-wrap gap-2" data-v-72139cbd><span class="px-3 py-1 text-sm rounded-full" style="background-color:#dc2626;color:#f8fafc;" data-v-72139cbd>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#374151;color:#f8fafc;" data-v-72139cbd>Vue.js</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#64748b;color:#f8fafc;" data-v-72139cbd>MySQL</span></div>',3))]),t("div",K,[t("div",Z,[(v(),u("svg",tt,o[13]||(o[13]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"},null,-1)])))]),o[14]||(o[14]=b('<h4 class="font-bold text-xl mb-3" style="color:#f8fafc;" data-v-72139cbd>Intern Tracker Tool</h4><p class="mb-4" style="color:#cbd5e1;" data-v-72139cbd>An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p><div class="flex flex-wrap gap-2" data-v-72139cbd><span class="px-3 py-1 text-sm rounded-full" style="background-color:#dc2626;color:#f8fafc;" data-v-72139cbd>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#374151;color:#f8fafc;" data-v-72139cbd>JavaScript</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#64748b;color:#f8fafc;" data-v-72139cbd>Bootstrap</span></div>',3))])])])]),o[16]||(o[16]=b('<section id="skills" class="py-20 px-8" style="background-color:#1a1a1a;" data-v-72139cbd><div class="max-w-6xl mx-auto" data-v-72139cbd><div class="text-center mb-16" data-v-72139cbd><h3 class="text-4xl font-bold mb-4" style="color:#f8fafc;" data-v-72139cbd>Skills &amp; Technologies</h3><p class="text-xl" style="color:#cbd5e1;" data-v-72139cbd>Technologies I work with to bring ideas to life</p></div><div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6" data-v-72139cbd><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-72139cbd><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#dc2626;" data-v-72139cbd><span class="font-bold text-lg" style="color:#f8fafc;" data-v-72139cbd>L</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-72139cbd>Laravel</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-72139cbd><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#374151;" data-v-72139cbd><span class="font-bold text-lg" style="color:#f8fafc;" data-v-72139cbd>V</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-72139cbd>Vue.js</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-72139cbd><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#64748b;" data-v-72139cbd><span class="font-bold text-lg" style="color:#f8fafc;" data-v-72139cbd>T</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-72139cbd>Tailwind CSS</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-72139cbd><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#dc2626;" data-v-72139cbd><span class="font-bold text-lg" style="color:#f8fafc;" data-v-72139cbd>U</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-72139cbd>UI/UX</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-72139cbd><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#374151;" data-v-72139cbd><span class="font-bold text-lg" style="color:#f8fafc;" data-v-72139cbd>G</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-72139cbd>Git</span></div></div></div></section>',1)),o[17]||(o[17]=t("section",{id:"contact",class:"py-20 px-8",style:{background:"linear-gradient(135deg, #dc2626 0%, #991b1b 100%)"}},[t("div",{class:"max-w-4xl mx-auto text-center"},[t("h3",{class:"text-4xl font-bold mb-6",style:{color:"#f8fafc"}},"Let's Work Together"),t("p",{class:"text-xl mb-8",style:{color:"#fca5a5"}},"Ready to bring your ideas to life? Let's discuss your next project."),t("div",{class:"flex flex-col sm:flex-row gap-4 justify-center"},[t("a",{href:"mailto:<EMAIL>",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{"background-color":"#f8fafc",color:"#dc2626"},onmouseover:"this.style.backgroundColor='#cbd5e1';",onmouseout:"this.style.backgroundColor='#f8fafc';"}," Send Email "),t("a",{href:"#",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{border:"2px solid #f8fafc",color:"#f8fafc","background-color":"transparent"},onmouseover:"this.style.backgroundColor='#f8fafc'; this.style.color='#dc2626';",onmouseout:"this.style.backgroundColor='transparent'; this.style.color='#f8fafc';"}," Download Resume ")])])],-1))])])}const st=A(B,[["render",ot],["__scopeId","data-v-72139cbd"]]);export{st as default};
