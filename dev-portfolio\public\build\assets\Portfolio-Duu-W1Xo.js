import{_ as w}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{f as i,o as d,b as t,n as S,k as f,F as m,l as y,e as M,m as v,p as C,q as $,s as x}from"./app-NMsZ_K_r.js";const j={name:"Portfolio",setup(){const u=v(!1),o=v(null),p=v(null),e=()=>{if(o.value){const l=o.value.offsetTop+o.value.offsetHeight,a=window.scrollY+100;u.value=a>=l}};return C(()=>{window.addEventListener("scroll",e),e()}),$(()=>{window.removeEventListener("scroll",e)}),{showStickyNav:u,heroSection:o,stickyNav:p,getParticleStyle:l=>{const a=Math.random()*4+1,r=Math.random()*100,n=Math.random()*20,c=Math.random()*10+10;return{width:`${a}px`,height:`${a}px`,left:`${r}%`,animationDelay:`${n}s`,animationDuration:`${c}s`}},getBinaryParticleStyle:l=>{const a=Math.random()*8+12,r=Math.random()*100,n=Math.random()*15,c=Math.random()*8+12;return{fontSize:`${a}px`,left:`${r}%`,animationDelay:`${n}s`,animationDuration:`${c}s`,color:`rgba(55, 65, 81, ${Math.random()*.5+.3})`}},getStarStyle:l=>{const a=Math.random()*3+1,r=Math.random()*100,n=Math.random()*100,c=Math.random()*10,h=Math.random()*5+3,k=Math.random()*.8+.2;return{width:`${a}px`,height:`${a}px`,left:`${r}%`,top:`${n}%`,animationDelay:`${c}s`,animationDuration:`${h}s`,opacity:k}},getDustParticleStyle:l=>{const a=Math.random()*2+.5,r=Math.random()*100,n=Math.random()*20,c=Math.random()*15+10;return{width:`${a}px`,height:`${a}px`,left:`${r}%`,animationDelay:`${n}s`,animationDuration:`${c}s`}},scrollToSection:l=>{const a=document.getElementById(l);a&&a.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"})}}}},D={style:{"background-color":"#0a0a0a",color:"#f8fafc"}},T={class:"max-w-7xl mx-auto flex justify-center items-center p-4"},_={class:"space-x-8"},z={id:"hero",ref:"heroSection",class:"relative min-h-screen flex flex-col"},P={class:"flex-1 flex items-center justify-center relative overflow-hidden",style:{background:"linear-gradient(135deg, #111827 0%, #0a0a0a 100%)"}},B={class:"absolute inset-0 overflow-hidden"},I={class:"starfield"},L={class:"cosmic-dust"},V={class:"relative z-10 text-center px-6 max-w-4xl",style:{color:"#f8fafc"}},N={class:"flex flex-col sm:flex-row gap-4 justify-center"},E={style:{"background-color":"#0a0a0a"}},U={id:"projects",class:"py-20 px-8"},H={class:"max-w-6xl mx-auto"},F={class:"grid md:grid-cols-2 gap-8"},G={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"#1a1a1a",border:"2px solid #2a2a2a"}},R={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#dc2626"}},A={class:"w-6 h-6",style:{color:"#f8fafc"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},W={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"#1a1a1a",border:"2px solid #2a2a2a"}},X={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#374151"}},q={class:"w-6 h-6",style:{color:"#f8fafc"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"};function J(u,o,p,e,b,g){return d(),i("div",D,[t("header",{ref:"stickyNav",class:S(["fixed top-0 left-0 right-0 z-50 shadow-lg transform -translate-y-full transition-transform duration-300",{"translate-y-0":e.showStickyNav}]),style:{"background-color":"#1a1a1a"}},[t("div",T,[t("nav",_,[t("button",{onClick:o[0]||(o[0]=s=>e.scrollToSection("hero")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Home"),t("button",{onClick:o[1]||(o[1]=s=>e.scrollToSection("projects")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Projects"),t("button",{onClick:o[2]||(o[2]=s=>e.scrollToSection("skills")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Skills"),t("button",{onClick:o[3]||(o[3]=s=>e.scrollToSection("contact")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Contact")])])],2),t("section",z,[t("div",P,[t("div",B,[t("div",I,[(d(),i(m,null,y(100,s=>t("div",{class:"star",key:"star-"+s,style:x(e.getStarStyle(s))},null,4)),64))]),o[6]||(o[6]=f('<div class="space-particles" data-v-a9068ef9><div class="particle code-particle particle-1" data-v-a9068ef9>{ }</div><div class="particle code-particle particle-2" data-v-a9068ef9>&lt;/&gt;</div><div class="particle code-particle particle-3" data-v-a9068ef9>( )</div><div class="particle code-particle particle-4" data-v-a9068ef9>[ ]</div><div class="particle code-particle particle-5" data-v-a9068ef9>=&gt;</div><div class="particle code-particle particle-6" data-v-a9068ef9>&amp;&amp;</div><div class="particle code-particle particle-7" data-v-a9068ef9>#</div><div class="particle code-particle particle-8" data-v-a9068ef9>$</div><div class="particle music-particle particle-9" data-v-a9068ef9>♪</div><div class="particle music-particle particle-10" data-v-a9068ef9>♫</div><div class="particle music-particle particle-11" data-v-a9068ef9>♬</div><div class="particle music-particle particle-12" data-v-a9068ef9>♩</div><div class="particle music-particle particle-13" data-v-a9068ef9>♭</div><div class="particle music-particle particle-14" data-v-a9068ef9>♯</div><div class="particle music-particle particle-15" data-v-a9068ef9>𝄞</div><div class="particle music-particle particle-16" data-v-a9068ef9>𝄢</div></div>',1)),t("div",L,[(d(),i(m,null,y(50,s=>t("div",{class:"dust-particle",key:"dust-"+s,style:x(e.getDustParticleStyle(s))},null,4)),64))]),o[7]||(o[7]=t("div",{class:"nebula-glow"},null,-1))]),t("div",V,[o[8]||(o[8]=t("h2",{class:"text-5xl md:text-7xl font-bold mb-6 leading-tight"},[M(" Hello! I am "),t("span",{class:"text-transparent bg-clip-text",style:{background:"linear-gradient(45deg, #dc2626, #fca5a5)","-webkit-background-clip":"text","background-clip":"text"}}," Isaac Martel. ")],-1)),o[9]||(o[9]=t("p",{class:"text-xl md:text-2xl mb-8 leading-relaxed",style:{color:"#cbd5e1"}}," I am a software engineer and full-stack web developer. ",-1)),t("div",N,[t("button",{onClick:o[4]||(o[4]=s=>e.scrollToSection("projects")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300",style:{"background-color":"#dc2626",color:"#f8fafc",border:"2px solid #dc2626"},onmouseover:"this.style.backgroundColor='#991b1b'; this.style.borderColor='#991b1b';",onmouseout:"this.style.backgroundColor='#dc2626'; this.style.borderColor='#dc2626';"}," View My Work "),t("button",{onClick:o[5]||(o[5]=s=>e.scrollToSection("contact")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300",style:{"background-color":"transparent",color:"#f8fafc",border:"2px solid #374151"},onmouseover:"this.style.backgroundColor='#374151'; this.style.borderColor='#374151';",onmouseout:"this.style.backgroundColor='transparent'; this.style.borderColor='#374151';"}," Get In Touch ")])])]),o[10]||(o[10]=t("div",{class:"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce"},[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))],512),t("main",E,[t("section",U,[t("div",H,[o[15]||(o[15]=t("div",{class:"text-center mb-16"},[t("h3",{class:"text-4xl font-bold mb-4",style:{color:"#f8fafc"}},"Featured Projects"),t("p",{class:"text-xl",style:{color:"#cbd5e1"}},"Some of my recent work and contributions")],-1)),t("div",F,[t("div",G,[t("div",R,[(d(),i("svg",A,o[11]||(o[11]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)])))]),o[12]||(o[12]=f('<h4 class="font-bold text-xl mb-3" style="color:#f8fafc;" data-v-a9068ef9>Online Student Clearance System</h4><p class="mb-4" style="color:#cbd5e1;" data-v-a9068ef9>A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p><div class="flex flex-wrap gap-2" data-v-a9068ef9><span class="px-3 py-1 text-sm rounded-full" style="background-color:#dc2626;color:#f8fafc;" data-v-a9068ef9>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#374151;color:#f8fafc;" data-v-a9068ef9>Vue.js</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#64748b;color:#f8fafc;" data-v-a9068ef9>MySQL</span></div>',3))]),t("div",W,[t("div",X,[(d(),i("svg",q,o[13]||(o[13]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"},null,-1)])))]),o[14]||(o[14]=f('<h4 class="font-bold text-xl mb-3" style="color:#f8fafc;" data-v-a9068ef9>Intern Tracker Tool</h4><p class="mb-4" style="color:#cbd5e1;" data-v-a9068ef9>An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p><div class="flex flex-wrap gap-2" data-v-a9068ef9><span class="px-3 py-1 text-sm rounded-full" style="background-color:#dc2626;color:#f8fafc;" data-v-a9068ef9>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#374151;color:#f8fafc;" data-v-a9068ef9>JavaScript</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#64748b;color:#f8fafc;" data-v-a9068ef9>Bootstrap</span></div>',3))])])])]),o[16]||(o[16]=f('<section id="skills" class="py-20 px-8" style="background-color:#1a1a1a;" data-v-a9068ef9><div class="max-w-6xl mx-auto" data-v-a9068ef9><div class="text-center mb-16" data-v-a9068ef9><h3 class="text-4xl font-bold mb-4" style="color:#f8fafc;" data-v-a9068ef9>Skills &amp; Technologies</h3><p class="text-xl" style="color:#cbd5e1;" data-v-a9068ef9>Technologies I work with to bring ideas to life</p></div><div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6" data-v-a9068ef9><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-a9068ef9><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#dc2626;" data-v-a9068ef9><span class="font-bold text-lg" style="color:#f8fafc;" data-v-a9068ef9>L</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-a9068ef9>Laravel</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-a9068ef9><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#374151;" data-v-a9068ef9><span class="font-bold text-lg" style="color:#f8fafc;" data-v-a9068ef9>V</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-a9068ef9>Vue.js</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-a9068ef9><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#64748b;" data-v-a9068ef9><span class="font-bold text-lg" style="color:#f8fafc;" data-v-a9068ef9>T</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-a9068ef9>Tailwind CSS</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-a9068ef9><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#dc2626;" data-v-a9068ef9><span class="font-bold text-lg" style="color:#f8fafc;" data-v-a9068ef9>U</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-a9068ef9>UI/UX</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-a9068ef9><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#374151;" data-v-a9068ef9><span class="font-bold text-lg" style="color:#f8fafc;" data-v-a9068ef9>G</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-a9068ef9>Git</span></div></div></div></section>',1)),o[17]||(o[17]=t("section",{id:"contact",class:"py-20 px-8",style:{background:"linear-gradient(135deg, #dc2626 0%, #991b1b 100%)"}},[t("div",{class:"max-w-4xl mx-auto text-center"},[t("h3",{class:"text-4xl font-bold mb-6",style:{color:"#f8fafc"}},"Let's Work Together"),t("p",{class:"text-xl mb-8",style:{color:"#fca5a5"}},"Ready to bring your ideas to life? Let's discuss your next project."),t("div",{class:"flex flex-col sm:flex-row gap-4 justify-center"},[t("a",{href:"mailto:<EMAIL>",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{"background-color":"#f8fafc",color:"#dc2626"},onmouseover:"this.style.backgroundColor='#cbd5e1';",onmouseout:"this.style.backgroundColor='#f8fafc';"}," Send Email "),t("a",{href:"#",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{border:"2px solid #f8fafc",color:"#f8fafc","background-color":"transparent"},onmouseover:"this.style.backgroundColor='#f8fafc'; this.style.color='#dc2626';",onmouseout:"this.style.backgroundColor='transparent'; this.style.color='#f8fafc';"}," Download Resume ")])])],-1))])])}const Z=w(j,[["render",J],["__scopeId","data-v-a9068ef9"]]);export{Z as default};
