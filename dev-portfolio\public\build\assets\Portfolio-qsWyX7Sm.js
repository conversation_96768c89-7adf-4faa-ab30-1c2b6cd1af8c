import{_ as g}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{f as b,o as v,b as o,n as k,k as m,l as a,m as w,p as S}from"./app-DBmCLojy.js";const C={name:"Portfolio",setup(){const f=a(!1),e=a(null),p=a(null),s=a(null),r=a(null),c=a(null),t=a(null),d=a(null),u=()=>{if(e.value){const n=e.value.offsetTop+e.value.offsetHeight,l=window.scrollY+100;f.value=l>=n}};w(()=>{window.addEventListener("scroll",u),u(),setTimeout(()=>{x()},300)}),S(()=>{window.removeEventListener("scroll",u)});const i=(n,l=0,y="slideUp")=>new Promise(h=>{setTimeout(()=>{n.classList.add("animate-in",`animate-${y}`),h()},l)}),x=async()=>{r.value&&c.value&&t.value&&d.value&&([r.value,c.value,t.value,d.value].forEach(l=>{l.classList.remove("animate-in","animate-slideUp","animate-slideDown","animate-fadeIn","animate-scaleIn")}),await i(r.value,200,"slideUp"),await i(c.value,400,"slideDown"),await i(t.value,800,"fadeIn"),await i(d.value,1200,"scaleIn"))};return{showStickyNav:f,heroSection:e,stickyNav:p,heroTitle:s,heroLine1:r,heroLine2:c,heroSubtitle:t,heroButtons:d,scrollToSection:n=>{const l=document.getElementById(n);l&&l.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"})}}}},_={style:{"background-color":"#000000",color:"#f8fafc"}},j={class:"max-w-7xl mx-auto flex justify-center items-center p-4"},T={class:"space-x-8"},L={id:"hero",ref:"heroSection",class:"relative min-h-screen flex flex-col"},I={class:"flex-1 flex items-center justify-center relative hero-background"},B={class:"relative z-10 text-center px-6 max-w-4xl hero-content",style:{color:"#f8fafc"}},M={ref:"heroTitle",class:"text-5xl md:text-7xl font-bold mb-6 leading-tight hero-title"},U={ref:"heroLine1",class:"hero-line hero-line-1"},V={ref:"heroLine2",class:"text-transparent bg-clip-text hero-line hero-line-2",style:{background:"linear-gradient(45deg, #2563eb, #93c5fd)","-webkit-background-clip":"text","background-clip":"text"}},E={ref:"heroSubtitle",class:"text-xl md:text-2xl mb-8 leading-relaxed hero-subtitle",style:{color:"#cbd5e1"}},z={ref:"heroButtons",class:"flex flex-col sm:flex-row gap-4 justify-center hero-buttons"},H={style:{"background-color":"#000000"}},N={id:"projects",class:"py-20 px-8"},P={class:"max-w-6xl mx-auto"},$={class:"grid md:grid-cols-2 gap-8"},A={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"#000000",border:"2px solid #333333"}},D={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#2563eb"}},G={class:"w-6 h-6",style:{color:"#f8fafc"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},R={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"#000000",border:"2px solid #333333"}},W={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#1d4ed8"}},X={class:"w-6 h-6",style:{color:"#f8fafc"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"};function F(f,e,p,s,r,c){return v(),b("div",_,[o("header",{ref:"stickyNav",class:k(["fixed top-0 left-0 right-0 z-50 shadow-lg transform -translate-y-full transition-transform duration-300",{"translate-y-0":s.showStickyNav}]),style:{"background-color":"#000000"}},[o("div",j,[o("nav",T,[o("button",{onClick:e[0]||(e[0]=t=>s.scrollToSection("hero")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#3b82f6';",onmouseout:"this.style.color='#cbd5e1';"},"Home"),o("button",{onClick:e[1]||(e[1]=t=>s.scrollToSection("projects")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#3b82f6';",onmouseout:"this.style.color='#cbd5e1';"},"Projects"),o("button",{onClick:e[2]||(e[2]=t=>s.scrollToSection("skills")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#3b82f6';",onmouseout:"this.style.color='#cbd5e1';"},"Skills"),o("button",{onClick:e[3]||(e[3]=t=>s.scrollToSection("contact")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#3b82f6';",onmouseout:"this.style.color='#cbd5e1';"},"Contact")])])],2),o("section",L,[o("div",I,[e[6]||(e[6]=o("div",{class:"absolute inset-0 bg-black bg-opacity-50"},null,-1)),o("div",B,[o("h2",M,[o("span",U,"Hello! I am",512),o("span",V," Isaac Martel. ",512)],512),o("p",E," I am a software engineer and full-stack web developer. ",512),o("div",z,[o("button",{onClick:e[4]||(e[4]=t=>s.scrollToSection("projects")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300 hero-button hero-button-1",style:{"background-color":"#2563eb",color:"#f8fafc",border:"2px solid #2563eb"},onmouseover:"this.style.backgroundColor='#1d4ed8'; this.style.borderColor='#1d4ed8';",onmouseout:"this.style.backgroundColor='#2563eb'; this.style.borderColor='#2563eb';"}," View My Work "),o("button",{onClick:e[5]||(e[5]=t=>s.scrollToSection("contact")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300 hero-button hero-button-2",style:{"background-color":"transparent",color:"#f8fafc",border:"2px solid #3b82f6"},onmouseover:"this.style.backgroundColor='#3b82f6'; this.style.borderColor='#3b82f6';",onmouseout:"this.style.backgroundColor='transparent'; this.style.borderColor='#3b82f6';"}," Get In Touch ")],512)])]),e[7]||(e[7]=o("div",{class:"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce",style:{color:"#3b82f6"}},[o("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))],512),o("main",H,[o("section",N,[o("div",P,[e[12]||(e[12]=o("div",{class:"text-center mb-16"},[o("h3",{class:"text-4xl font-bold mb-4",style:{color:"#f8fafc"}},"Featured Projects"),o("p",{class:"text-xl",style:{color:"#cbd5e1"}},"Some of my recent work and contributions")],-1)),o("div",$,[o("div",A,[o("div",D,[(v(),b("svg",G,e[8]||(e[8]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)])))]),e[9]||(e[9]=m('<h4 class="font-bold text-xl mb-3" style="color:#f8fafc;" data-v-4f49605a>Online Student Clearance System</h4><p class="mb-4" style="color:#cbd5e1;" data-v-4f49605a>A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p><div class="flex flex-wrap gap-2" data-v-4f49605a><span class="px-3 py-1 text-sm rounded-full" style="background-color:#2563eb;color:#f8fafc;" data-v-4f49605a>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#3b82f6;color:#f8fafc;" data-v-4f49605a>Vue.js</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#60a5fa;color:#f8fafc;" data-v-4f49605a>MySQL</span></div>',3))]),o("div",R,[o("div",W,[(v(),b("svg",X,e[10]||(e[10]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"},null,-1)])))]),e[11]||(e[11]=m('<h4 class="font-bold text-xl mb-3" style="color:#f8fafc;" data-v-4f49605a>Intern Tracker Tool</h4><p class="mb-4" style="color:#cbd5e1;" data-v-4f49605a>An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p><div class="flex flex-wrap gap-2" data-v-4f49605a><span class="px-3 py-1 text-sm rounded-full" style="background-color:#2563eb;color:#f8fafc;" data-v-4f49605a>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#3b82f6;color:#f8fafc;" data-v-4f49605a>JavaScript</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#60a5fa;color:#f8fafc;" data-v-4f49605a>Bootstrap</span></div>',3))])])])]),e[13]||(e[13]=m('<section id="skills" class="py-20 px-8" style="background-color:#000000;" data-v-4f49605a><div class="max-w-6xl mx-auto" data-v-4f49605a><div class="text-center mb-16" data-v-4f49605a><h3 class="text-4xl font-bold mb-4" style="color:#f8fafc;" data-v-4f49605a>Skills &amp; Technologies</h3><p class="text-xl" style="color:#cbd5e1;" data-v-4f49605a>Technologies I work with to bring ideas to life</p></div><div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6" data-v-4f49605a><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#000000;border:1px solid #333333;" data-v-4f49605a><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#2563eb;" data-v-4f49605a><span class="font-bold text-lg" style="color:#f8fafc;" data-v-4f49605a>L</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-4f49605a>Laravel</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#000000;border:1px solid #333333;" data-v-4f49605a><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#3b82f6;" data-v-4f49605a><span class="font-bold text-lg" style="color:#f8fafc;" data-v-4f49605a>V</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-4f49605a>Vue.js</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#000000;border:1px solid #333333;" data-v-4f49605a><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#60a5fa;" data-v-4f49605a><span class="font-bold text-lg" style="color:#f8fafc;" data-v-4f49605a>T</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-4f49605a>Tailwind CSS</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#000000;border:1px solid #333333;" data-v-4f49605a><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#1d4ed8;" data-v-4f49605a><span class="font-bold text-lg" style="color:#f8fafc;" data-v-4f49605a>U</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-4f49605a>UI/UX</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#000000;border:1px solid #333333;" data-v-4f49605a><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#1e40af;" data-v-4f49605a><span class="font-bold text-lg" style="color:#f8fafc;" data-v-4f49605a>G</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-4f49605a>Git</span></div></div></div></section>',1)),e[14]||(e[14]=o("section",{id:"contact",class:"py-20 px-8",style:{background:"linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)"}},[o("div",{class:"max-w-4xl mx-auto text-center"},[o("h3",{class:"text-4xl font-bold mb-6",style:{color:"#f8fafc"}},"Let's Work Together"),o("p",{class:"text-xl mb-8",style:{color:"#93c5fd"}},"Ready to bring your ideas to life? Let's discuss your next project."),o("div",{class:"flex flex-col sm:flex-row gap-4 justify-center"},[o("a",{href:"mailto:<EMAIL>",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{"background-color":"#f8fafc",color:"#2563eb"},onmouseover:"this.style.backgroundColor='#cbd5e1';",onmouseout:"this.style.backgroundColor='#f8fafc';"}," Send Email "),o("a",{href:"#",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{border:"2px solid #f8fafc",color:"#f8fafc","background-color":"transparent"},onmouseover:"this.style.backgroundColor='#f8fafc'; this.style.color='#2563eb';",onmouseout:"this.style.backgroundColor='transparent'; this.style.color='#f8fafc';"}," Download Resume ")])])],-1))])])}const Y=g(C,[["render",F],["__scopeId","data-v-4f49605a"]]);export{Y as default};
