import{_ as k}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{f as u,o as p,b as o,n as w,k as b,l as c,m as C,p as T}from"./app-CO18qU9D.js";const _={name:"Portfolio",setup(){const d=c(!1),t=c(null),v=c(null),e=c(null),a=c(null),r=c(null),l=()=>{if(t.value){const s=t.value.offsetTop+t.value.offsetHeight,n=window.scrollY+100;d.value=n>=s}};C(()=>{window.addEventListener("scroll",l),l(),setTimeout(()=>{x()},500)}),T(()=>{window.removeEventListener("scroll",l)});const i=(s,n,m=100,y=!1)=>new Promise(g=>{let f=0;s.textContent="";const h=setInterval(()=>{f<n.length?(s.textContent+=n.charAt(f),f++):(clearInterval(h),y&&setTimeout(()=>{s.classList.add("typing-complete")},1e3),g())},m)}),x=async()=>{e.value&&a.value&&r.value&&(e.value.textContent="",a.value.textContent="",r.value.textContent="",e.value.classList.remove("typing-complete"),a.value.classList.remove("typing-complete"),r.value.classList.remove("typing-complete"),await i(e.value,"Hello! I am ",80),e.value.classList.add("typing-complete"),await new Promise(s=>setTimeout(s,200)),await i(a.value,"Isaac Martel.",100),a.value.classList.add("typing-complete"),await new Promise(s=>setTimeout(s,800)),await i(r.value,"I am a software engineer and full-stack web developer.",60,!0))};return{showStickyNav:d,heroSection:t,stickyNav:v,typingText1:e,typingText2:a,typingText3:r,scrollToSection:s=>{const n=document.getElementById(s);n&&n.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"})}}}},S={style:{"background-color":"#000000",color:"#f8fafc"}},j={class:"max-w-7xl mx-auto flex justify-center items-center p-4"},L={class:"space-x-8"},I={id:"hero",ref:"heroSection",class:"relative min-h-screen flex flex-col"},B={class:"flex-1 flex items-center justify-center relative hero-background"},M={class:"relative z-10 text-center px-6 max-w-4xl",style:{color:"#f8fafc"}},P={class:"text-5xl md:text-7xl font-bold mb-6 leading-tight"},V={ref:"typingText1",class:"typing-text"},z={class:"text-transparent bg-clip-text",style:{background:"linear-gradient(45deg, #2563eb, #93c5fd)","-webkit-background-clip":"text","background-clip":"text"}},E={ref:"typingText2",class:"typing-text"},N={class:"text-xl md:text-2xl mb-8 leading-relaxed",style:{color:"#cbd5e1"}},U={ref:"typingText3",class:"typing-text"},H={class:"flex flex-col sm:flex-row gap-4 justify-center"},A={style:{"background-color":"#000000"}},G={id:"projects",class:"py-20 px-8"},R={class:"max-w-6xl mx-auto"},$={class:"grid md:grid-cols-2 gap-8"},W={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"#000000",border:"2px solid #333333"}},X={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#2563eb"}},D={class:"w-6 h-6",style:{color:"#f8fafc"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},F={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"#000000",border:"2px solid #333333"}},J={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#1d4ed8"}},O={class:"w-6 h-6",style:{color:"#f8fafc"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"};function Q(d,t,v,e,a,r){return p(),u("div",S,[o("header",{ref:"stickyNav",class:w(["fixed top-0 left-0 right-0 z-50 shadow-lg transform -translate-y-full transition-transform duration-300",{"translate-y-0":e.showStickyNav}]),style:{"background-color":"#000000"}},[o("div",j,[o("nav",L,[o("button",{onClick:t[0]||(t[0]=l=>e.scrollToSection("hero")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#3b82f6';",onmouseout:"this.style.color='#cbd5e1';"},"Home"),o("button",{onClick:t[1]||(t[1]=l=>e.scrollToSection("projects")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#3b82f6';",onmouseout:"this.style.color='#cbd5e1';"},"Projects"),o("button",{onClick:t[2]||(t[2]=l=>e.scrollToSection("skills")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#3b82f6';",onmouseout:"this.style.color='#cbd5e1';"},"Skills"),o("button",{onClick:t[3]||(t[3]=l=>e.scrollToSection("contact")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#3b82f6';",onmouseout:"this.style.color='#cbd5e1';"},"Contact")])])],2),o("section",I,[o("div",B,[t[6]||(t[6]=o("div",{class:"absolute inset-0 bg-black bg-opacity-50"},null,-1)),o("div",M,[o("h2",P,[o("span",V,null,512),o("span",z,[o("span",E,null,512)])]),o("p",N,[o("span",U,null,512)]),o("div",H,[o("button",{onClick:t[4]||(t[4]=l=>e.scrollToSection("projects")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300",style:{"background-color":"#2563eb",color:"#f8fafc",border:"2px solid #2563eb"},onmouseover:"this.style.backgroundColor='#1d4ed8'; this.style.borderColor='#1d4ed8';",onmouseout:"this.style.backgroundColor='#2563eb'; this.style.borderColor='#2563eb';"}," View My Work "),o("button",{onClick:t[5]||(t[5]=l=>e.scrollToSection("contact")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300",style:{"background-color":"transparent",color:"#f8fafc",border:"2px solid #3b82f6"},onmouseover:"this.style.backgroundColor='#3b82f6'; this.style.borderColor='#3b82f6';",onmouseout:"this.style.backgroundColor='transparent'; this.style.borderColor='#3b82f6';"}," Get In Touch ")])])]),t[7]||(t[7]=o("div",{class:"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce",style:{color:"#3b82f6"}},[o("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))],512),o("main",A,[o("section",G,[o("div",R,[t[12]||(t[12]=o("div",{class:"text-center mb-16"},[o("h3",{class:"text-4xl font-bold mb-4",style:{color:"#f8fafc"}},"Featured Projects"),o("p",{class:"text-xl",style:{color:"#cbd5e1"}},"Some of my recent work and contributions")],-1)),o("div",$,[o("div",W,[o("div",X,[(p(),u("svg",D,t[8]||(t[8]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)])))]),t[9]||(t[9]=b('<h4 class="font-bold text-xl mb-3" style="color:#f8fafc;" data-v-95c10d96>Online Student Clearance System</h4><p class="mb-4" style="color:#cbd5e1;" data-v-95c10d96>A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p><div class="flex flex-wrap gap-2" data-v-95c10d96><span class="px-3 py-1 text-sm rounded-full" style="background-color:#2563eb;color:#f8fafc;" data-v-95c10d96>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#3b82f6;color:#f8fafc;" data-v-95c10d96>Vue.js</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#60a5fa;color:#f8fafc;" data-v-95c10d96>MySQL</span></div>',3))]),o("div",F,[o("div",J,[(p(),u("svg",O,t[10]||(t[10]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"},null,-1)])))]),t[11]||(t[11]=b('<h4 class="font-bold text-xl mb-3" style="color:#f8fafc;" data-v-95c10d96>Intern Tracker Tool</h4><p class="mb-4" style="color:#cbd5e1;" data-v-95c10d96>An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p><div class="flex flex-wrap gap-2" data-v-95c10d96><span class="px-3 py-1 text-sm rounded-full" style="background-color:#2563eb;color:#f8fafc;" data-v-95c10d96>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#3b82f6;color:#f8fafc;" data-v-95c10d96>JavaScript</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#60a5fa;color:#f8fafc;" data-v-95c10d96>Bootstrap</span></div>',3))])])])]),t[13]||(t[13]=b('<section id="skills" class="py-20 px-8" style="background-color:#000000;" data-v-95c10d96><div class="max-w-6xl mx-auto" data-v-95c10d96><div class="text-center mb-16" data-v-95c10d96><h3 class="text-4xl font-bold mb-4" style="color:#f8fafc;" data-v-95c10d96>Skills &amp; Technologies</h3><p class="text-xl" style="color:#cbd5e1;" data-v-95c10d96>Technologies I work with to bring ideas to life</p></div><div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6" data-v-95c10d96><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#000000;border:1px solid #333333;" data-v-95c10d96><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#2563eb;" data-v-95c10d96><span class="font-bold text-lg" style="color:#f8fafc;" data-v-95c10d96>L</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-95c10d96>Laravel</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#000000;border:1px solid #333333;" data-v-95c10d96><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#3b82f6;" data-v-95c10d96><span class="font-bold text-lg" style="color:#f8fafc;" data-v-95c10d96>V</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-95c10d96>Vue.js</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#000000;border:1px solid #333333;" data-v-95c10d96><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#60a5fa;" data-v-95c10d96><span class="font-bold text-lg" style="color:#f8fafc;" data-v-95c10d96>T</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-95c10d96>Tailwind CSS</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#000000;border:1px solid #333333;" data-v-95c10d96><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#1d4ed8;" data-v-95c10d96><span class="font-bold text-lg" style="color:#f8fafc;" data-v-95c10d96>U</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-95c10d96>UI/UX</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#000000;border:1px solid #333333;" data-v-95c10d96><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#1e40af;" data-v-95c10d96><span class="font-bold text-lg" style="color:#f8fafc;" data-v-95c10d96>G</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-95c10d96>Git</span></div></div></div></section>',1)),t[14]||(t[14]=o("section",{id:"contact",class:"py-20 px-8",style:{background:"linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)"}},[o("div",{class:"max-w-4xl mx-auto text-center"},[o("h3",{class:"text-4xl font-bold mb-6",style:{color:"#f8fafc"}},"Let's Work Together"),o("p",{class:"text-xl mb-8",style:{color:"#93c5fd"}},"Ready to bring your ideas to life? Let's discuss your next project."),o("div",{class:"flex flex-col sm:flex-row gap-4 justify-center"},[o("a",{href:"mailto:<EMAIL>",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{"background-color":"#f8fafc",color:"#2563eb"},onmouseover:"this.style.backgroundColor='#cbd5e1';",onmouseout:"this.style.backgroundColor='#f8fafc';"}," Send Email "),o("a",{href:"#",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{border:"2px solid #f8fafc",color:"#f8fafc","background-color":"transparent"},onmouseover:"this.style.backgroundColor='#f8fafc'; this.style.color='#2563eb';",onmouseout:"this.style.backgroundColor='transparent'; this.style.color='#f8fafc';"}," Download Resume ")])])],-1))])])}const Z=k(_,[["render",Q],["__scopeId","data-v-95c10d96"]]);export{Z as default};
