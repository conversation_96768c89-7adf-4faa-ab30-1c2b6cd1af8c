import{_ as g}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{f as i,o as v,b as t,k as o,n as u,F as m,l as b,m as r,p as f,q as w,s as y}from"./app-Ck7u0fWp.js";const k={name:"Portfolio",setup(){const l=r(!1),a=r(null),n=r(null),s=()=>{if(a.value){const d=a.value.offsetTop+a.value.offsetHeight,e=window.scrollY+100;l.value=e>=d}};return f(()=>{window.addEventListener("scroll",s),s()}),w(()=>{window.removeEventListener("scroll",s)}),{showStickyNav:l,heroSection:a,stickyNav:n,getParticleStyle:d=>{const e=Math.random()*4+1,x=Math.random()*100,p=Math.random()*20,h=Math.random()*10+10;return{width:`${e}px`,height:`${e}px`,left:`${x}%`,animationDelay:`${p}s`,animationDuration:`${h}s`}}}}},j={class:"bg-gray-100 text-gray-800"},S={id:"hero",ref:"heroSection",class:"relative min-h-screen flex flex-col"},L={class:"flex-1 flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"},M={class:"absolute inset-0 overflow-hidden"},_={class:"particles"};function T(l,a,n,s,c,d){return v(),i("div",j,[t("header",{ref:"stickyNav",class:u(["fixed top-0 left-0 right-0 z-50 bg-white shadow-lg transform -translate-y-full transition-transform duration-300",{"translate-y-0":s.showStickyNav}])},a[0]||(a[0]=[o('<div class="max-w-7xl mx-auto flex justify-between items-center p-4" data-v-9950454a><h1 class="text-xl font-bold text-gray-800" data-v-9950454a></h1><nav class="space-x-6" data-v-9950454a><a href="#hero" class="text-gray-700 hover:text-blue-600 transition-colors" data-v-9950454a>Home</a><a href="#projects" class="text-gray-700 hover:text-blue-600 transition-colors" data-v-9950454a>Projects</a><a href="#skills" class="text-gray-700 hover:text-blue-600 transition-colors" data-v-9950454a>Skills</a><a href="#contact" class="text-gray-700 hover:text-blue-600 transition-colors" data-v-9950454a>Contact</a></nav></div>',1)]),2),t("section",S,[t("div",L,[t("div",M,[a[1]||(a[1]=o('<div class="floating-shapes" data-v-9950454a><div class="shape shape-1" data-v-9950454a></div><div class="shape shape-2" data-v-9950454a></div><div class="shape shape-3" data-v-9950454a></div><div class="shape shape-4" data-v-9950454a></div><div class="shape shape-5" data-v-9950454a></div><div class="shape shape-6" data-v-9950454a></div><div class="shape shape-7" data-v-9950454a></div><div class="shape shape-8" data-v-9950454a></div></div><div class="grid-background" data-v-9950454a></div>',2)),t("div",_,[(v(),i(m,null,b(50,e=>t("div",{class:"particle",key:e,style:y(s.getParticleStyle(e))},null,4)),64))])]),a[2]||(a[2]=o('<div class="relative z-10 text-center text-white px-6 max-w-4xl" data-v-9950454a><h2 class="text-5xl md:text-7xl font-bold mb-6 leading-tight" data-v-9950454a> Hi, I&#39;m a <span class="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400" data-v-9950454a> Web Developer </span></h2><p class="text-xl md:text-2xl mb-8 text-gray-200 leading-relaxed" data-v-9950454a> I create modern and responsive web applications using Laravel, Vue, and Tailwind CSS </p><div class="flex flex-col sm:flex-row gap-4 justify-center" data-v-9950454a><a href="#projects" class="bg-white/10 backdrop-blur-sm border border-white/20 text-white px-8 py-3 rounded-full font-semibold hover:bg-white/20 transition-all duration-300" data-v-9950454a> View My Work </a><a href="#contact" class="border-2 border-white/30 text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-purple-900 transition-all duration-300" data-v-9950454a> Get In Touch </a></div></div>',1))]),a[3]||(a[3]=t("div",{class:"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce"},[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))],512),a[4]||(a[4]=o('<main class="bg-white" data-v-9950454a><section id="projects" class="py-20 px-8" data-v-9950454a><div class="max-w-6xl mx-auto" data-v-9950454a><div class="text-center mb-16" data-v-9950454a><h3 class="text-4xl font-bold mb-4 text-gray-800" data-v-9950454a>Featured Projects</h3><p class="text-xl text-gray-600" data-v-9950454a>Some of my recent work and contributions</p></div><div class="grid md:grid-cols-2 gap-8" data-v-9950454a><div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow border border-gray-100" data-v-9950454a><div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6" data-v-9950454a><svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-9950454a><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" data-v-9950454a></path></svg></div><h4 class="font-bold text-xl mb-3 text-gray-800" data-v-9950454a>Online Student Clearance System</h4><p class="text-gray-600 mb-4" data-v-9950454a>A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p><div class="flex flex-wrap gap-2" data-v-9950454a><span class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full" data-v-9950454a>Laravel</span><span class="px-3 py-1 bg-green-100 text-green-700 text-sm rounded-full" data-v-9950454a>Vue.js</span><span class="px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded-full" data-v-9950454a>MySQL</span></div></div><div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow border border-gray-100" data-v-9950454a><div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6" data-v-9950454a><svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-9950454a><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" data-v-9950454a></path></svg></div><h4 class="font-bold text-xl mb-3 text-gray-800" data-v-9950454a>Intern Tracker Tool</h4><p class="text-gray-600 mb-4" data-v-9950454a>An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p><div class="flex flex-wrap gap-2" data-v-9950454a><span class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full" data-v-9950454a>Laravel</span><span class="px-3 py-1 bg-yellow-100 text-yellow-700 text-sm rounded-full" data-v-9950454a>JavaScript</span><span class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full" data-v-9950454a>Bootstrap</span></div></div></div></div></section><section id="skills" class="py-20 px-8 bg-gray-50" data-v-9950454a><div class="max-w-6xl mx-auto" data-v-9950454a><div class="text-center mb-16" data-v-9950454a><h3 class="text-4xl font-bold mb-4 text-gray-800" data-v-9950454a>Skills &amp; Technologies</h3><p class="text-xl text-gray-600" data-v-9950454a>Technologies I work with to bring ideas to life</p></div><div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6" data-v-9950454a><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-9950454a><div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-9950454a><span class="text-red-600 font-bold text-lg" data-v-9950454a>L</span></div><span class="font-semibold text-gray-800" data-v-9950454a>Laravel</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-9950454a><div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-9950454a><span class="text-green-600 font-bold text-lg" data-v-9950454a>V</span></div><span class="font-semibold text-gray-800" data-v-9950454a>Vue.js</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-9950454a><div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-9950454a><span class="text-blue-600 font-bold text-lg" data-v-9950454a>T</span></div><span class="font-semibold text-gray-800" data-v-9950454a>Tailwind CSS</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-9950454a><div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-9950454a><span class="text-yellow-600 font-bold text-lg" data-v-9950454a>U</span></div><span class="font-semibold text-gray-800" data-v-9950454a>UI/UX</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-9950454a><div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-9950454a><span class="text-gray-600 font-bold text-lg" data-v-9950454a>G</span></div><span class="font-semibold text-gray-800" data-v-9950454a>Git</span></div></div></div></section><section id="contact" class="py-20 px-8 bg-gradient-to-br from-blue-600 to-purple-600" data-v-9950454a><div class="max-w-4xl mx-auto text-center" data-v-9950454a><h3 class="text-4xl font-bold mb-6 text-white" data-v-9950454a>Let&#39;s Work Together</h3><p class="text-xl text-blue-100 mb-8" data-v-9950454a>Ready to bring your ideas to life? Let&#39;s discuss your next project.</p><div class="flex flex-col sm:flex-row gap-4 justify-center" data-v-9950454a><a href="mailto:<EMAIL>" class="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors" data-v-9950454a> Send Email </a><a href="#" class="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-colors" data-v-9950454a> Download Resume </a></div></div></section></main>',1))])}const C=g(k,[["render",T],["__scopeId","data-v-9950454a"]]);export{C as default};
