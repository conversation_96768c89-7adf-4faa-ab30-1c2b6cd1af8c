import{_ as k}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{f as x,o as g,b as s,k as i,n as S,F as M,l as j,m as f,p as $,q as L,s as B,t as P}from"./app-BHpD2pUp.js";const D={name:"Portfolio",setup(){const c=f(!1),t=f(null),m=f(null),l=()=>{if(t.value){const a=t.value.offsetTop+t.value.offsetHeight,e=window.scrollY+100;c.value=e>=a}};return $(()=>{window.addEventListener("scroll",l),l()}),L(()=>{window.removeEventListener("scroll",l)}),{showStickyNav:c,heroSection:t,stickyNav:m,getParticleStyle:a=>{const e=Math.random()*4+1,n=Math.random()*100,d=Math.random()*20,o=Math.random()*10+10;return{width:`${e}px`,height:`${e}px`,left:`${n}%`,animationDelay:`${d}s`,animationDuration:`${o}s`}},getBinaryParticleStyle:a=>{const e=Math.random()*8+12,n=Math.random()*100,d=Math.random()*15,o=Math.random()*8+12;return{fontSize:`${e}px`,left:`${n}%`,animationDelay:`${d}s`,animationDuration:`${o}s`,color:`rgba(30, 58, 138, ${Math.random()*.5+.3})`}},handleMouseMove:a=>{const e=a.currentTarget.getBoundingClientRect(),n=(a.clientX-e.left)/e.width,d=(a.clientY-e.top)/e.height;for(let o=1;o<=12;o++){const r=document.querySelector(`.element-${o}`);if(r){const h=(d-.5)*25,b=(n-.5)*25,y=(n-.5)*15,w=(d-.5)*15;r.style.animationPlayState="paused",r.style.transform=`
            translateX(${y}px)
            translateY(${w}px)
            rotateX(${h}deg)
            rotateY(${b}deg)
            scale(1.05)
          `,r.style.transition="transform 0.1s ease-out, color 0.2s ease-out, text-shadow 0.2s ease-out",r.style.color="rgba(96, 165, 250, 0.8)",r.style.textShadow="0 0 20px rgba(96, 165, 250, 0.6)"}}},handleMouseLeave:()=>{for(let a=1;a<=12;a++){const e=document.querySelector(`.element-${a}`);e&&(e.style.animationPlayState="running",e.style.transform="",e.style.transition="transform 0.5s ease-out, color 0.3s ease-out, text-shadow 0.3s ease-out",e.style.color="rgba(30, 58, 138, 0.7)",e.style.textShadow="0 0 15px rgba(30, 58, 138, 0.5)")}}}}},T={class:"bg-gray-100 text-gray-800"},_={id:"hero",ref:"heroSection",class:"relative min-h-screen flex flex-col"},z={class:"flex-1 flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-slate-900 via-navy-900 to-slate-900"},C={class:"absolute inset-0 overflow-hidden"},V={class:"binary-particles"};function I(c,t,m,l,p,u){return g(),x("div",T,[s("header",{ref:"stickyNav",class:S(["fixed top-0 left-0 right-0 z-50 bg-white shadow-lg transform -translate-y-full transition-transform duration-300",{"translate-y-0":l.showStickyNav}])},t[0]||(t[0]=[i('<div class="max-w-7xl mx-auto flex justify-between items-center p-4" data-v-41fe0552><h1 class="text-xl font-bold text-gray-800" data-v-41fe0552></h1><nav class="space-x-6" data-v-41fe0552><a href="#hero" class="text-gray-700 hover:text-blue-600 transition-colors" data-v-41fe0552>Home</a><a href="#projects" class="text-gray-700 hover:text-blue-600 transition-colors" data-v-41fe0552>Projects</a><a href="#skills" class="text-gray-700 hover:text-blue-600 transition-colors" data-v-41fe0552>Skills</a><a href="#contact" class="text-gray-700 hover:text-blue-600 transition-colors" data-v-41fe0552>Contact</a></nav></div>',1)]),2),s("section",_,[s("div",z,[s("div",C,[t[1]||(t[1]=i('<div class="floating-elements" data-v-41fe0552><div class="element code-element element-1" data-v-41fe0552>{ }</div><div class="element code-element element-2" data-v-41fe0552>&lt;/&gt;</div><div class="element code-element element-3" data-v-41fe0552>( )</div><div class="element code-element element-4" data-v-41fe0552>[ ]</div><div class="element music-element element-5" data-v-41fe0552>♪</div><div class="element music-element element-6" data-v-41fe0552>♫</div><div class="element music-element element-7" data-v-41fe0552>♬</div><div class="element music-element element-8" data-v-41fe0552>♩</div><div class="element symbol-element element-9" data-v-41fe0552>=&gt;</div><div class="element symbol-element element-10" data-v-41fe0552>&amp;&amp;</div><div class="element symbol-element element-11" data-v-41fe0552>#</div><div class="element symbol-element element-12" data-v-41fe0552>$</div></div><div class="code-grid-background" data-v-41fe0552></div>',2)),s("div",V,[(g(),x(M,null,j(30,v=>s("div",{class:"binary-particle",key:v,style:B(l.getBinaryParticleStyle(v))},P(Math.random()>.5?"1":"0"),5)),64))])]),t[2]||(t[2]=i('<div class="relative z-10 text-center text-white px-6 max-w-4xl" data-v-41fe0552><h2 class="text-5xl md:text-7xl font-bold mb-6 leading-tight" data-v-41fe0552> Hi, I&#39;m a <span class="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-blue-400 to-indigo-400" data-v-41fe0552> Web Developer </span></h2><p class="text-xl md:text-2xl mb-8 text-gray-200 leading-relaxed" data-v-41fe0552> I create modern and responsive web applications using Laravel, Vue, and Tailwind CSS </p><div class="flex flex-col sm:flex-row gap-4 justify-center" data-v-41fe0552><a href="#projects" class="bg-white/10 backdrop-blur-sm border border-white/20 text-white px-8 py-3 rounded-full font-semibold hover:bg-white/20 transition-all duration-300" data-v-41fe0552> View My Work </a><a href="#contact" class="border-2 border-white/30 text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-navy-900 transition-all duration-300" data-v-41fe0552> Get In Touch </a></div></div>',1))]),t[3]||(t[3]=s("div",{class:"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce"},[s("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))],512),t[4]||(t[4]=i('<main class="bg-white" data-v-41fe0552><section id="projects" class="py-20 px-8" data-v-41fe0552><div class="max-w-6xl mx-auto" data-v-41fe0552><div class="text-center mb-16" data-v-41fe0552><h3 class="text-4xl font-bold mb-4 text-gray-800" data-v-41fe0552>Featured Projects</h3><p class="text-xl text-gray-600" data-v-41fe0552>Some of my recent work and contributions</p></div><div class="grid md:grid-cols-2 gap-8" data-v-41fe0552><div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow border border-gray-100" data-v-41fe0552><div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6" data-v-41fe0552><svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-41fe0552><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" data-v-41fe0552></path></svg></div><h4 class="font-bold text-xl mb-3 text-gray-800" data-v-41fe0552>Online Student Clearance System</h4><p class="text-gray-600 mb-4" data-v-41fe0552>A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p><div class="flex flex-wrap gap-2" data-v-41fe0552><span class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full" data-v-41fe0552>Laravel</span><span class="px-3 py-1 bg-green-100 text-green-700 text-sm rounded-full" data-v-41fe0552>Vue.js</span><span class="px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded-full" data-v-41fe0552>MySQL</span></div></div><div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow border border-gray-100" data-v-41fe0552><div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6" data-v-41fe0552><svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-41fe0552><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" data-v-41fe0552></path></svg></div><h4 class="font-bold text-xl mb-3 text-gray-800" data-v-41fe0552>Intern Tracker Tool</h4><p class="text-gray-600 mb-4" data-v-41fe0552>An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p><div class="flex flex-wrap gap-2" data-v-41fe0552><span class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full" data-v-41fe0552>Laravel</span><span class="px-3 py-1 bg-yellow-100 text-yellow-700 text-sm rounded-full" data-v-41fe0552>JavaScript</span><span class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full" data-v-41fe0552>Bootstrap</span></div></div></div></div></section><section id="skills" class="py-20 px-8 bg-gray-50" data-v-41fe0552><div class="max-w-6xl mx-auto" data-v-41fe0552><div class="text-center mb-16" data-v-41fe0552><h3 class="text-4xl font-bold mb-4 text-gray-800" data-v-41fe0552>Skills &amp; Technologies</h3><p class="text-xl text-gray-600" data-v-41fe0552>Technologies I work with to bring ideas to life</p></div><div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6" data-v-41fe0552><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-41fe0552><div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-41fe0552><span class="text-red-600 font-bold text-lg" data-v-41fe0552>L</span></div><span class="font-semibold text-gray-800" data-v-41fe0552>Laravel</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-41fe0552><div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-41fe0552><span class="text-green-600 font-bold text-lg" data-v-41fe0552>V</span></div><span class="font-semibold text-gray-800" data-v-41fe0552>Vue.js</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-41fe0552><div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-41fe0552><span class="text-blue-600 font-bold text-lg" data-v-41fe0552>T</span></div><span class="font-semibold text-gray-800" data-v-41fe0552>Tailwind CSS</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-41fe0552><div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-41fe0552><span class="text-yellow-600 font-bold text-lg" data-v-41fe0552>U</span></div><span class="font-semibold text-gray-800" data-v-41fe0552>UI/UX</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-41fe0552><div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-41fe0552><span class="text-gray-600 font-bold text-lg" data-v-41fe0552>G</span></div><span class="font-semibold text-gray-800" data-v-41fe0552>Git</span></div></div></div></section><section id="contact" class="py-20 px-8 bg-gradient-to-br from-blue-600 to-purple-600" data-v-41fe0552><div class="max-w-4xl mx-auto text-center" data-v-41fe0552><h3 class="text-4xl font-bold mb-6 text-white" data-v-41fe0552>Let&#39;s Work Together</h3><p class="text-xl text-blue-100 mb-8" data-v-41fe0552>Ready to bring your ideas to life? Let&#39;s discuss your next project.</p><div class="flex flex-col sm:flex-row gap-4 justify-center" data-v-41fe0552><a href="mailto:<EMAIL>" class="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors" data-v-41fe0552> Send Email </a><a href="#" class="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-colors" data-v-41fe0552> Download Resume </a></div></div></section></main>',1))])}const E=k(D,[["render",I],["__scopeId","data-v-41fe0552"]]);export{E as default};
