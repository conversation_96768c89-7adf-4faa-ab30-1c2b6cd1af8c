import{_ as r}from"./AuthenticatedLayout-DQW3H7g-.js";import{f as o,o as d,a as e,u as l,g as i,w as s,b as a,F as m}from"./app-BXK8RfwS.js";import"./ApplicationLogo-DCmhy0GK.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const _={__name:"Dashboard",setup(n){return(g,t)=>(d(),o(m,null,[e(l(i),{title:"Dashboard"}),e(r,null,{header:s(()=>t[0]||(t[0]=[a("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Dashboard ",-1)])),default:s(()=>[t[1]||(t[1]=a("div",{class:"py-12"},[a("div",{class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},[a("div",{class:"overflow-hidden bg-white shadow-sm sm:rounded-lg dark:bg-gray-800"},[a("div",{class:"p-6 text-gray-900 dark:text-gray-100"}," You're logged in! ")])])],-1))]),_:1,__:[1]})],64))}};export{_ as default};
