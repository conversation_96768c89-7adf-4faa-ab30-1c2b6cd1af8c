import{A as a}from"./ApplicationLogo-Cz6jsetF.js";import{f as r,o,b as e,a as s,w as l,u as c,S as n,r as d}from"./app-BSq5vkZr.js";const i={class:"flex min-h-screen flex-col items-center bg-gray-100 pt-6 sm:justify-center sm:pt-0 dark:bg-gray-900"},m={class:"mt-6 w-full overflow-hidden bg-white px-6 py-4 shadow-md sm:max-w-md sm:rounded-lg dark:bg-gray-800"},g={__name:"GuestLayout",setup(f){return(t,u)=>(o(),r("div",i,[e("div",null,[s(c(n),{href:"/"},{default:l(()=>[s(a,{class:"h-20 w-20 fill-current text-gray-500"})]),_:1})]),e("div",m,[d(t.$slots,"default")])]))}};export{g as _};
