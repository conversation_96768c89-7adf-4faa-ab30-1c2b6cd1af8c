import{_ as u}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{f as d,o as c,b as t,n as v,k as l,e as p,l as n,m as b,p as m}from"./app-j1Im5gpg.js";const x={name:"Portfolio",setup(){const r=n(!1),o=n(null),i=n(null),s=()=>{if(o.value){const e=o.value.offsetTop+o.value.offsetHeight,a=window.scrollY+100;r.value=a>=e}};return b(()=>{window.addEventListener("scroll",s),s()}),m(()=>{window.removeEventListener("scroll",s)}),{showStickyNav:r,heroSection:o,stickyNav:i,scrollToSection:e=>{const a=document.getElementById(e);a&&a.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"})}}}},y={style:{"background-color":"#0a0a0a",color:"#f8fafc"}},g={class:"max-w-7xl mx-auto flex justify-center items-center p-4"},k={class:"space-x-8"},h={id:"hero",ref:"heroSection",class:"relative min-h-screen flex flex-col"},w={class:"flex-1 flex items-center justify-center relative",style:{background:"linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%)"}},C={class:"relative z-10 text-center px-6 max-w-4xl",style:{color:"#f8fafc"}},S={class:"flex flex-col sm:flex-row gap-4 justify-center"},j={style:{"background-color":"#0a0a0a"}},T={id:"projects",class:"py-20 px-8"},_={class:"max-w-6xl mx-auto"},I={class:"grid md:grid-cols-2 gap-8"},B={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"#1a1a1a",border:"2px solid #2a2a2a"}},L={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#dc2626"}},V={class:"w-6 h-6",style:{color:"#f8fafc"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},M={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"#1a1a1a",border:"2px solid #2a2a2a"}},N={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#374151"}},z={class:"w-6 h-6",style:{color:"#f8fafc"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"};function E(r,o,i,s,f,e){return c(),d("div",y,[t("header",{ref:"stickyNav",class:v(["fixed top-0 left-0 right-0 z-50 shadow-lg transform -translate-y-full transition-transform duration-300",{"translate-y-0":s.showStickyNav}]),style:{"background-color":"#1a1a1a"}},[t("div",g,[t("nav",k,[t("button",{onClick:o[0]||(o[0]=a=>s.scrollToSection("hero")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Home"),t("button",{onClick:o[1]||(o[1]=a=>s.scrollToSection("projects")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Projects"),t("button",{onClick:o[2]||(o[2]=a=>s.scrollToSection("skills")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Skills"),t("button",{onClick:o[3]||(o[3]=a=>s.scrollToSection("contact")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Contact")])])],2),t("section",h,[t("div",w,[o[8]||(o[8]=l('<div class="absolute inset-0 overflow-hidden" data-v-a30308c1><div class="absolute top-10 left-10 red-smoke smoke-1" data-v-a30308c1><div class="absolute w-40 h-60 bg-red-600 rounded-full blur-3xl smoke-wisp" style="opacity:0.15;transform:rotate(15deg);" data-v-a30308c1></div><div class="absolute w-30 h-45 bg-red-500 rounded-full blur-3xl smoke-wisp" style="opacity:0.12;left:25px;top:-30px;transform:rotate(-10deg);" data-v-a30308c1></div><div class="absolute w-35 h-50 bg-red-700 rounded-full blur-3xl smoke-wisp" style="opacity:0.13;left:10px;top:-20px;transform:rotate(25deg);" data-v-a30308c1></div><div class="absolute w-25 h-35 bg-red-400 rounded-full blur-2xl smoke-wisp" style="opacity:0.10;left:40px;top:15px;transform:rotate(-20deg);" data-v-a30308c1></div></div><div class="absolute bottom-20 right-20 red-smoke smoke-2" data-v-a30308c1><div class="absolute w-35 h-50 bg-red-500 rounded-full blur-3xl smoke-wisp" style="opacity:0.14;transform:rotate(-30deg);" data-v-a30308c1></div><div class="absolute w-28 h-40 bg-red-400 rounded-full blur-2xl smoke-wisp" style="opacity:0.11;left:30px;top:-25px;transform:rotate(45deg);" data-v-a30308c1></div><div class="absolute w-32 h-45 bg-red-600 rounded-full blur-3xl smoke-wisp" style="opacity:0.12;left:5px;top:-15px;transform:rotate(-15deg);" data-v-a30308c1></div></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 red-smoke smoke-3" data-v-a30308c1><div class="absolute w-60 h-25 bg-red-700 rounded-full blur-3xl smoke-wisp" style="opacity:0.08;transform:rotate(5deg);" data-v-a30308c1></div><div class="absolute w-45 h-20 bg-red-800 rounded-full blur-2xl smoke-wisp" style="opacity:0.06;left:40px;top:-12px;transform:rotate(-25deg);" data-v-a30308c1></div><div class="absolute w-50 h-18 bg-red-600 rounded-full blur-3xl smoke-wisp" style="opacity:0.07;left:10px;top:-8px;transform:rotate(35deg);" data-v-a30308c1></div></div><div class="absolute top-1/4 right-1/4 red-smoke smoke-4" data-v-a30308c1><div class="absolute w-30 h-55 bg-red-400 rounded-full blur-3xl smoke-wisp" style="opacity:0.11;transform:rotate(20deg);" data-v-a30308c1></div><div class="absolute w-25 h-40 bg-red-300 rounded-full blur-2xl smoke-wisp" style="opacity:0.08;left:20px;top:-30px;transform:rotate(-40deg);" data-v-a30308c1></div><div class="absolute w-22 h-35 bg-red-500 rounded-full blur-3xl smoke-wisp" style="opacity:0.09;left:8px;top:-20px;transform:rotate(60deg);" data-v-a30308c1></div></div><div class="absolute bottom-1/4 left-1/4 red-smoke smoke-5" data-v-a30308c1><div class="absolute w-38 h-42 bg-red-800 rounded-full blur-3xl smoke-wisp" style="opacity:0.07;transform:rotate(-45deg);" data-v-a30308c1></div><div class="absolute w-32 h-38 bg-red-900 rounded-full blur-3xl smoke-wisp" style="opacity:0.05;left:25px;top:-20px;transform:rotate(30deg);" data-v-a30308c1></div><div class="absolute w-28 h-32 bg-red-600 rounded-full blur-2xl smoke-wisp" style="opacity:0.06;left:5px;top:-12px;transform:rotate(-60deg);" data-v-a30308c1></div></div><div class="absolute top-3/4 right-10 red-smoke smoke-6" data-v-a30308c1><div class="absolute w-20 h-35 bg-red-300 rounded-full blur-2xl smoke-wisp" style="opacity:0.05;transform:rotate(10deg);" data-v-a30308c1></div><div class="absolute w-16 h-28 bg-red-400 rounded-full blur-2xl smoke-wisp" style="opacity:0.04;left:15px;top:-18px;transform:rotate(-50deg);" data-v-a30308c1></div></div><div class="absolute top-10 right-1/3 red-smoke smoke-7" data-v-a30308c1><div class="absolute w-25 h-40 bg-red-500 rounded-full blur-3xl smoke-wisp" style="opacity:0.06;transform:rotate(75deg);" data-v-a30308c1></div></div></div>',1)),t("div",C,[o[6]||(o[6]=t("h2",{class:"text-5xl md:text-7xl font-bold mb-6 leading-tight"},[p(" Hello! I am "),t("span",{class:"text-transparent bg-clip-text",style:{background:"linear-gradient(45deg, #dc2626, #fca5a5)","-webkit-background-clip":"text","background-clip":"text"}}," Isaac Martel. ")],-1)),o[7]||(o[7]=t("p",{class:"text-xl md:text-2xl mb-8 leading-relaxed",style:{color:"#cbd5e1"}}," I am a software engineer and full-stack web developer. ",-1)),t("div",S,[t("button",{onClick:o[4]||(o[4]=a=>s.scrollToSection("projects")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300",style:{"background-color":"#dc2626",color:"#f8fafc",border:"2px solid #dc2626"},onmouseover:"this.style.backgroundColor='#991b1b'; this.style.borderColor='#991b1b';",onmouseout:"this.style.backgroundColor='#dc2626'; this.style.borderColor='#dc2626';"}," View My Work "),t("button",{onClick:o[5]||(o[5]=a=>s.scrollToSection("contact")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300",style:{"background-color":"transparent",color:"#f8fafc",border:"2px solid #374151"},onmouseover:"this.style.backgroundColor='#374151'; this.style.borderColor='#374151';",onmouseout:"this.style.backgroundColor='transparent'; this.style.borderColor='#374151';"}," Get In Touch ")])])]),o[9]||(o[9]=t("div",{class:"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce"},[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))],512),t("main",j,[t("section",T,[t("div",_,[o[14]||(o[14]=t("div",{class:"text-center mb-16"},[t("h3",{class:"text-4xl font-bold mb-4",style:{color:"#f8fafc"}},"Featured Projects"),t("p",{class:"text-xl",style:{color:"#cbd5e1"}},"Some of my recent work and contributions")],-1)),t("div",I,[t("div",B,[t("div",L,[(c(),d("svg",V,o[10]||(o[10]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)])))]),o[11]||(o[11]=l('<h4 class="font-bold text-xl mb-3" style="color:#f8fafc;" data-v-a30308c1>Online Student Clearance System</h4><p class="mb-4" style="color:#cbd5e1;" data-v-a30308c1>A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p><div class="flex flex-wrap gap-2" data-v-a30308c1><span class="px-3 py-1 text-sm rounded-full" style="background-color:#dc2626;color:#f8fafc;" data-v-a30308c1>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#374151;color:#f8fafc;" data-v-a30308c1>Vue.js</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#64748b;color:#f8fafc;" data-v-a30308c1>MySQL</span></div>',3))]),t("div",M,[t("div",N,[(c(),d("svg",z,o[12]||(o[12]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"},null,-1)])))]),o[13]||(o[13]=l('<h4 class="font-bold text-xl mb-3" style="color:#f8fafc;" data-v-a30308c1>Intern Tracker Tool</h4><p class="mb-4" style="color:#cbd5e1;" data-v-a30308c1>An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p><div class="flex flex-wrap gap-2" data-v-a30308c1><span class="px-3 py-1 text-sm rounded-full" style="background-color:#dc2626;color:#f8fafc;" data-v-a30308c1>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#374151;color:#f8fafc;" data-v-a30308c1>JavaScript</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#64748b;color:#f8fafc;" data-v-a30308c1>Bootstrap</span></div>',3))])])])]),o[15]||(o[15]=l('<section id="skills" class="py-20 px-8" style="background-color:#1a1a1a;" data-v-a30308c1><div class="max-w-6xl mx-auto" data-v-a30308c1><div class="text-center mb-16" data-v-a30308c1><h3 class="text-4xl font-bold mb-4" style="color:#f8fafc;" data-v-a30308c1>Skills &amp; Technologies</h3><p class="text-xl" style="color:#cbd5e1;" data-v-a30308c1>Technologies I work with to bring ideas to life</p></div><div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6" data-v-a30308c1><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-a30308c1><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#dc2626;" data-v-a30308c1><span class="font-bold text-lg" style="color:#f8fafc;" data-v-a30308c1>L</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-a30308c1>Laravel</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-a30308c1><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#374151;" data-v-a30308c1><span class="font-bold text-lg" style="color:#f8fafc;" data-v-a30308c1>V</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-a30308c1>Vue.js</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-a30308c1><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#64748b;" data-v-a30308c1><span class="font-bold text-lg" style="color:#f8fafc;" data-v-a30308c1>T</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-a30308c1>Tailwind CSS</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-a30308c1><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#dc2626;" data-v-a30308c1><span class="font-bold text-lg" style="color:#f8fafc;" data-v-a30308c1>U</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-a30308c1>UI/UX</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-a30308c1><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#374151;" data-v-a30308c1><span class="font-bold text-lg" style="color:#f8fafc;" data-v-a30308c1>G</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-a30308c1>Git</span></div></div></div></section>',1)),o[16]||(o[16]=t("section",{id:"contact",class:"py-20 px-8",style:{background:"linear-gradient(135deg, #dc2626 0%, #991b1b 100%)"}},[t("div",{class:"max-w-4xl mx-auto text-center"},[t("h3",{class:"text-4xl font-bold mb-6",style:{color:"#f8fafc"}},"Let's Work Together"),t("p",{class:"text-xl mb-8",style:{color:"#fca5a5"}},"Ready to bring your ideas to life? Let's discuss your next project."),t("div",{class:"flex flex-col sm:flex-row gap-4 justify-center"},[t("a",{href:"mailto:<EMAIL>",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{"background-color":"#f8fafc",color:"#dc2626"},onmouseover:"this.style.backgroundColor='#cbd5e1';",onmouseout:"this.style.backgroundColor='#f8fafc';"}," Send Email "),t("a",{href:"#",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{border:"2px solid #f8fafc",color:"#f8fafc","background-color":"transparent"},onmouseover:"this.style.backgroundColor='#f8fafc'; this.style.color='#dc2626';",onmouseout:"this.style.backgroundColor='transparent'; this.style.color='#f8fafc';"}," Download Resume ")])])],-1))])])}const H=u(x,[["render",E],["__scopeId","data-v-a30308c1"]]);export{H as default};
