html[data-v-9950454a]{scroll-behavior:smooth}section[data-v-9950454a]{scroll-margin-top:80px}.floating-shapes[data-v-9950454a]{position:absolute;width:100%;height:100%;overflow:hidden}.shape[data-v-9950454a]{position:absolute;background:linear-gradient(45deg,#8b5cf64d,#3b82f64d);border-radius:20px;animation:float-9950454a 20s infinite linear;-webkit-backdrop-filter:blur(1px);backdrop-filter:blur(1px)}.shape-1[data-v-9950454a]{width:80px;height:80px;top:20%;left:10%;animation-delay:0s;transform:rotateX(45deg) rotateY(45deg)}.shape-2[data-v-9950454a]{width:120px;height:120px;top:60%;left:80%;animation-delay:-5s;transform:rotateX(-30deg) rotateY(60deg)}.shape-3[data-v-9950454a]{width:60px;height:60px;top:80%;left:20%;animation-delay:-10s;transform:rotateX(60deg) rotateY(-45deg)}.shape-4[data-v-9950454a]{width:100px;height:100px;top:10%;left:70%;animation-delay:-15s;transform:rotateX(-45deg) rotateY(30deg)}.shape-5[data-v-9950454a]{width:90px;height:90px;top:40%;left:5%;animation-delay:-3s;transform:rotateX(30deg) rotateY(-60deg)}.shape-6[data-v-9950454a]{width:70px;height:70px;top:70%;left:60%;animation-delay:-8s;transform:rotateX(-60deg) rotateY(45deg)}.shape-7[data-v-9950454a]{width:110px;height:110px;top:30%;left:40%;animation-delay:-12s;transform:rotateX(45deg) rotateY(-30deg)}.shape-8[data-v-9950454a]{width:85px;height:85px;top:90%;left:90%;animation-delay:-18s;transform:rotateX(-30deg) rotateY(60deg)}@keyframes float-9950454a{0%{transform:translateY(0) rotateX(0) rotateY(0) rotate(0)}25%{transform:translateY(-20px) rotateX(90deg) rotateY(90deg) rotate(90deg)}50%{transform:translateY(0) rotateX(180deg) rotateY(180deg) rotate(180deg)}75%{transform:translateY(-10px) rotateX(270deg) rotateY(270deg) rotate(270deg)}to{transform:translateY(0) rotateX(360deg) rotateY(360deg) rotate(360deg)}}.grid-background[data-v-9950454a]{position:absolute;top:0;left:0;width:100%;height:100%;background-image:linear-gradient(rgba(139,92,246,.1) 1px,transparent 1px),linear-gradient(90deg,rgba(139,92,246,.1) 1px,transparent 1px);background-size:50px 50px;animation:gridMove-9950454a 20s linear infinite}@keyframes gridMove-9950454a{0%{transform:translate(0)}to{transform:translate(50px,50px)}}.particles[data-v-9950454a]{position:absolute;width:100%;height:100%;overflow:hidden}.particle[data-v-9950454a]{position:absolute;background:#fffc;border-radius:50%;animation:particleFloat-9950454a 15s infinite linear}@keyframes particleFloat-9950454a{0%{transform:translateY(100vh) rotate(0);opacity:0}10%{opacity:1}90%{opacity:1}to{transform:translateY(-100px) rotate(360deg);opacity:0}}
