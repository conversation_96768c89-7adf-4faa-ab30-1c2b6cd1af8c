import{_ as k}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{f as x,o as h,b as e,k as f,n as M,F as S,l as _,m as v,p as j,q as L,s as $,t as B}from"./app-Bl3zMDxa.js";const P={name:"Portfolio",setup(){const c=v(!1),t=v(null),m=v(null),n=()=>{if(t.value){const s=t.value.offsetTop+t.value.offsetHeight,a=window.scrollY+100;c.value=a>=s}};return j(()=>{window.addEventListener("scroll",n),n()}),L(()=>{window.removeEventListener("scroll",n)}),{showStickyNav:c,heroSection:t,stickyNav:m,getParticleStyle:s=>{const a=Math.random()*4+1,r=Math.random()*100,i=Math.random()*20,l=Math.random()*10+10;return{width:`${a}px`,height:`${a}px`,left:`${r}%`,animationDelay:`${i}s`,animationDuration:`${l}s`}},getBinaryParticleStyle:s=>{const a=Math.random()*8+12,r=Math.random()*100,i=Math.random()*15,l=Math.random()*8+12;return{fontSize:`${a}px`,left:`${r}%`,animationDelay:`${i}s`,animationDuration:`${l}s`,color:`rgba(30, 58, 138, ${Math.random()*.5+.3})`}},handleMouseMove:s=>{const a=s.currentTarget.getBoundingClientRect(),r=(s.clientX-a.left)/a.width,i=(s.clientY-a.top)/a.height;for(let l=1;l<=12;l++){const d=document.querySelector(`.element-${l}`);if(d){const p=(i-.5)*25,b=(r-.5)*25,y=(r-.5)*15,w=(i-.5)*15;d.style.animationPlayState="paused",d.style.transform=`
            translateX(${y}px)
            translateY(${w}px)
            rotateX(${p}deg)
            rotateY(${b}deg)
            scale(1.05)
          `,d.style.transition="transform 0.1s ease-out",d.style.filter="brightness(1.2)"}}},handleMouseLeave:()=>{for(let s=1;s<=12;s++){const a=document.querySelector(`.element-${s}`);a&&(a.style.animationPlayState="running",a.style.transform="",a.style.transition="transform 0.5s ease-out, filter 0.3s ease-out",a.style.filter="brightness(1)")}}}}},D={class:"bg-gray-100 text-gray-800"},T={id:"hero",ref:"heroSection",class:"relative min-h-screen flex flex-col"},z={class:"flex-1 flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-slate-900 via-navy-900 to-slate-900"},C={class:"absolute inset-0 overflow-hidden"},V={class:"element code-element element-1",ref:"element1"},I={class:"element code-element element-2",ref:"element2"},X={class:"element code-element element-3",ref:"element3"},N={class:"element code-element element-4",ref:"element4"},Y={class:"element music-element element-5",ref:"element5"},U={class:"element music-element element-6",ref:"element6"},E={class:"element music-element element-7",ref:"element7"},H={class:"element music-element element-8",ref:"element8"},R={class:"element symbol-element element-9",ref:"element9"},q={class:"element symbol-element element-10",ref:"element10"},F={class:"element symbol-element element-11",ref:"element11"},G={class:"element symbol-element element-12",ref:"element12"},W={class:"binary-particles"};function A(c,t,m,n,g,u){return h(),x("div",D,[e("header",{ref:"stickyNav",class:M(["fixed top-0 left-0 right-0 z-50 bg-white shadow-lg transform -translate-y-full transition-transform duration-300",{"translate-y-0":n.showStickyNav}])},t[2]||(t[2]=[f('<div class="max-w-7xl mx-auto flex justify-between items-center p-4" data-v-8f77fe0c><h1 class="text-xl font-bold text-gray-800" data-v-8f77fe0c></h1><nav class="space-x-6" data-v-8f77fe0c><a href="#hero" class="text-gray-700 hover:text-blue-600 transition-colors" data-v-8f77fe0c>Home</a><a href="#projects" class="text-gray-700 hover:text-blue-600 transition-colors" data-v-8f77fe0c>Projects</a><a href="#skills" class="text-gray-700 hover:text-blue-600 transition-colors" data-v-8f77fe0c>Skills</a><a href="#contact" class="text-gray-700 hover:text-blue-600 transition-colors" data-v-8f77fe0c>Contact</a></nav></div>',1)]),2),e("section",T,[e("div",z,[e("div",C,[e("div",{class:"floating-elements",onMousemove:t[0]||(t[0]=(...o)=>n.handleMouseMove&&n.handleMouseMove(...o)),onMouseleave:t[1]||(t[1]=(...o)=>n.handleMouseLeave&&n.handleMouseLeave(...o))},[e("div",V,"{ }",512),e("div",I,"</>",512),e("div",X,"( )",512),e("div",N,"[ ]",512),e("div",Y,"♪",512),e("div",U,"♫",512),e("div",E,"♬",512),e("div",H,"♩",512),e("div",R,"=>",512),e("div",q,"&&",512),e("div",F,"#",512),e("div",G,"$",512)],32),t[3]||(t[3]=e("div",{class:"code-grid-background"},null,-1)),e("div",W,[(h(),x(S,null,_(30,o=>e("div",{class:"binary-particle",key:o,style:$(n.getBinaryParticleStyle(o))},B(Math.random()>.5?"1":"0"),5)),64))])]),t[4]||(t[4]=f('<div class="relative z-10 text-center text-white px-6 max-w-4xl" data-v-8f77fe0c><h2 class="text-5xl md:text-7xl font-bold mb-6 leading-tight" data-v-8f77fe0c> Hi, I&#39;m a <span class="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-blue-400 to-indigo-400" data-v-8f77fe0c> Web Developer </span></h2><p class="text-xl md:text-2xl mb-8 text-gray-200 leading-relaxed" data-v-8f77fe0c> I create modern and responsive web applications using Laravel, Vue, and Tailwind CSS </p><div class="flex flex-col sm:flex-row gap-4 justify-center" data-v-8f77fe0c><a href="#projects" class="bg-white/10 backdrop-blur-sm border border-white/20 text-white px-8 py-3 rounded-full font-semibold hover:bg-white/20 transition-all duration-300" data-v-8f77fe0c> View My Work </a><a href="#contact" class="border-2 border-white/30 text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-navy-900 transition-all duration-300" data-v-8f77fe0c> Get In Touch </a></div></div>',1))]),t[5]||(t[5]=e("div",{class:"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce"},[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))],512),t[6]||(t[6]=f('<main class="bg-white" data-v-8f77fe0c><section id="projects" class="py-20 px-8" data-v-8f77fe0c><div class="max-w-6xl mx-auto" data-v-8f77fe0c><div class="text-center mb-16" data-v-8f77fe0c><h3 class="text-4xl font-bold mb-4 text-gray-800" data-v-8f77fe0c>Featured Projects</h3><p class="text-xl text-gray-600" data-v-8f77fe0c>Some of my recent work and contributions</p></div><div class="grid md:grid-cols-2 gap-8" data-v-8f77fe0c><div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow border border-gray-100" data-v-8f77fe0c><div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6" data-v-8f77fe0c><svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-8f77fe0c><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" data-v-8f77fe0c></path></svg></div><h4 class="font-bold text-xl mb-3 text-gray-800" data-v-8f77fe0c>Online Student Clearance System</h4><p class="text-gray-600 mb-4" data-v-8f77fe0c>A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p><div class="flex flex-wrap gap-2" data-v-8f77fe0c><span class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full" data-v-8f77fe0c>Laravel</span><span class="px-3 py-1 bg-green-100 text-green-700 text-sm rounded-full" data-v-8f77fe0c>Vue.js</span><span class="px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded-full" data-v-8f77fe0c>MySQL</span></div></div><div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow border border-gray-100" data-v-8f77fe0c><div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6" data-v-8f77fe0c><svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-8f77fe0c><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" data-v-8f77fe0c></path></svg></div><h4 class="font-bold text-xl mb-3 text-gray-800" data-v-8f77fe0c>Intern Tracker Tool</h4><p class="text-gray-600 mb-4" data-v-8f77fe0c>An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p><div class="flex flex-wrap gap-2" data-v-8f77fe0c><span class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full" data-v-8f77fe0c>Laravel</span><span class="px-3 py-1 bg-yellow-100 text-yellow-700 text-sm rounded-full" data-v-8f77fe0c>JavaScript</span><span class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full" data-v-8f77fe0c>Bootstrap</span></div></div></div></div></section><section id="skills" class="py-20 px-8 bg-gray-50" data-v-8f77fe0c><div class="max-w-6xl mx-auto" data-v-8f77fe0c><div class="text-center mb-16" data-v-8f77fe0c><h3 class="text-4xl font-bold mb-4 text-gray-800" data-v-8f77fe0c>Skills &amp; Technologies</h3><p class="text-xl text-gray-600" data-v-8f77fe0c>Technologies I work with to bring ideas to life</p></div><div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6" data-v-8f77fe0c><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-8f77fe0c><div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-8f77fe0c><span class="text-red-600 font-bold text-lg" data-v-8f77fe0c>L</span></div><span class="font-semibold text-gray-800" data-v-8f77fe0c>Laravel</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-8f77fe0c><div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-8f77fe0c><span class="text-green-600 font-bold text-lg" data-v-8f77fe0c>V</span></div><span class="font-semibold text-gray-800" data-v-8f77fe0c>Vue.js</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-8f77fe0c><div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-8f77fe0c><span class="text-blue-600 font-bold text-lg" data-v-8f77fe0c>T</span></div><span class="font-semibold text-gray-800" data-v-8f77fe0c>Tailwind CSS</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-8f77fe0c><div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-8f77fe0c><span class="text-yellow-600 font-bold text-lg" data-v-8f77fe0c>U</span></div><span class="font-semibold text-gray-800" data-v-8f77fe0c>UI/UX</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-8f77fe0c><div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-8f77fe0c><span class="text-gray-600 font-bold text-lg" data-v-8f77fe0c>G</span></div><span class="font-semibold text-gray-800" data-v-8f77fe0c>Git</span></div></div></div></section><section id="contact" class="py-20 px-8 bg-gradient-to-br from-blue-600 to-purple-600" data-v-8f77fe0c><div class="max-w-4xl mx-auto text-center" data-v-8f77fe0c><h3 class="text-4xl font-bold mb-6 text-white" data-v-8f77fe0c>Let&#39;s Work Together</h3><p class="text-xl text-blue-100 mb-8" data-v-8f77fe0c>Ready to bring your ideas to life? Let&#39;s discuss your next project.</p><div class="flex flex-col sm:flex-row gap-4 justify-center" data-v-8f77fe0c><a href="mailto:<EMAIL>" class="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors" data-v-8f77fe0c> Send Email </a><a href="#" class="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-colors" data-v-8f77fe0c> Download Resume </a></div></div></section></main>',1))])}const Z=k(P,[["render",A],["__scopeId","data-v-8f77fe0c"]]);export{Z as default};
