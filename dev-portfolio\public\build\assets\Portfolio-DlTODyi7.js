import{_ as x}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{f as n,o as d,b as o,n as p,k as r,F as g,l as h,e as k,m,p as w,q as S,s as C,t as j}from"./app-Cs9yi0p2.js";const M={name:"Portfolio",setup(){const c=m(!1),t=m(null),v=m(null),e=()=>{if(t.value){const a=t.value.offsetTop+t.value.offsetHeight,l=window.scrollY+100;c.value=l>=a}};return w(()=>{window.addEventListener("scroll",e),e()}),S(()=>{window.removeEventListener("scroll",e)}),{showStickyNav:c,heroSection:t,stickyNav:v,getParticleStyle:a=>{const l=Math.random()*4+1,i=Math.random()*100,f=Math.random()*20,b=Math.random()*10+10;return{width:`${l}px`,height:`${l}px`,left:`${i}%`,animationDelay:`${f}s`,animationDuration:`${b}s`}},getBinaryParticleStyle:a=>{const l=Math.random()*8+12,i=Math.random()*100,f=Math.random()*15,b=Math.random()*8+12;return{fontSize:`${l}px`,left:`${i}%`,animationDelay:`${f}s`,animationDuration:`${b}s`,color:`rgba(55, 65, 81, ${Math.random()*.5+.3})`}},scrollToSection:a=>{const l=document.getElementById(a);l&&l.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"})}}}},T={style:{"background-color":"#0a0a0a",color:"#f8fafc"}},_={class:"max-w-7xl mx-auto flex justify-between items-center p-4"},$={class:"space-x-6"},B={id:"hero",ref:"heroSection",class:"relative min-h-screen flex flex-col"},z={class:"flex-1 flex items-center justify-center relative overflow-hidden",style:{background:"linear-gradient(135deg, #111827 0%, #0a0a0a 100%)"}},D={class:"absolute inset-0 overflow-hidden"},I={class:"binary-particles"},L={class:"relative z-10 text-center px-6 max-w-4xl",style:{color:"#f8fafc"}},P={class:"flex flex-col sm:flex-row gap-4 justify-center"},V={style:{"background-color":"#0a0a0a"}},N={id:"projects",class:"py-20 px-8"},E={class:"max-w-6xl mx-auto"},U={class:"grid md:grid-cols-2 gap-8"},H={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"#1a1a1a",border:"2px solid #2a2a2a"}},F={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#dc2626"}},G={class:"w-6 h-6",style:{color:"#f8fafc"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},R={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"#1a1a1a",border:"2px solid #2a2a2a"}},A={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#374151"}},W={class:"w-6 h-6",style:{color:"#f8fafc"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"};function X(c,t,v,e,u,y){return d(),n("div",T,[o("header",{ref:"stickyNav",class:p(["fixed top-0 left-0 right-0 z-50 shadow-lg transform -translate-y-full transition-transform duration-300",{"translate-y-0":e.showStickyNav}]),style:{"background-color":"#1a1a1a"}},[o("div",_,[o("nav",$,[o("button",{onClick:t[0]||(t[0]=s=>e.scrollToSection("hero")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Home"),o("button",{onClick:t[1]||(t[1]=s=>e.scrollToSection("projects")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Projects"),o("button",{onClick:t[2]||(t[2]=s=>e.scrollToSection("skills")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Skills"),o("button",{onClick:t[3]||(t[3]=s=>e.scrollToSection("contact")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Contact")])])],2),o("section",B,[o("div",z,[o("div",D,[t[6]||(t[6]=r('<div class="floating-elements" data-v-29b2d732><div class="element code-element element-1" data-v-29b2d732>{ }</div><div class="element code-element element-2" data-v-29b2d732>&lt;/&gt;</div><div class="element code-element element-3" data-v-29b2d732>( )</div><div class="element code-element element-4" data-v-29b2d732>[ ]</div><div class="element music-element element-5" data-v-29b2d732>♪</div><div class="element music-element element-6" data-v-29b2d732>♫</div><div class="element music-element element-7" data-v-29b2d732>♬</div><div class="element music-element element-8" data-v-29b2d732>♩</div><div class="element symbol-element element-9" data-v-29b2d732>=&gt;</div><div class="element symbol-element element-10" data-v-29b2d732>&amp;&amp;</div><div class="element symbol-element element-11" data-v-29b2d732>#</div><div class="element symbol-element element-12" data-v-29b2d732>$</div></div><div class="code-grid-background" data-v-29b2d732></div>',2)),o("div",I,[(d(),n(g,null,h(30,s=>o("div",{class:"binary-particle",key:s,style:C(e.getBinaryParticleStyle(s))},j(Math.random()>.5?"1":"0"),5)),64))])]),o("div",L,[t[7]||(t[7]=o("h2",{class:"text-5xl md:text-7xl font-bold mb-6 leading-tight"},[k(" Hello! I am "),o("span",{class:"text-transparent bg-clip-text",style:{background:"linear-gradient(45deg, #dc2626, #fca5a5)","-webkit-background-clip":"text","background-clip":"text"}}," Isaac Martel. ")],-1)),t[8]||(t[8]=o("p",{class:"text-xl md:text-2xl mb-8 leading-relaxed",style:{color:"#cbd5e1"}}," I am a software engineer and full-stack web developer. ",-1)),o("div",P,[o("button",{onClick:t[4]||(t[4]=s=>e.scrollToSection("projects")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300",style:{"background-color":"#dc2626",color:"#f8fafc",border:"2px solid #dc2626"},onmouseover:"this.style.backgroundColor='#991b1b'; this.style.borderColor='#991b1b';",onmouseout:"this.style.backgroundColor='#dc2626'; this.style.borderColor='#dc2626';"}," View My Work "),o("button",{onClick:t[5]||(t[5]=s=>e.scrollToSection("contact")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300",style:{"background-color":"transparent",color:"#f8fafc",border:"2px solid #374151"},onmouseover:"this.style.backgroundColor='#374151'; this.style.borderColor='#374151';",onmouseout:"this.style.backgroundColor='transparent'; this.style.borderColor='#374151';"}," Get In Touch ")])])]),t[9]||(t[9]=o("div",{class:"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce"},[o("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))],512),o("main",V,[o("section",N,[o("div",E,[t[14]||(t[14]=o("div",{class:"text-center mb-16"},[o("h3",{class:"text-4xl font-bold mb-4",style:{color:"#f8fafc"}},"Featured Projects"),o("p",{class:"text-xl",style:{color:"#cbd5e1"}},"Some of my recent work and contributions")],-1)),o("div",U,[o("div",H,[o("div",F,[(d(),n("svg",G,t[10]||(t[10]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)])))]),t[11]||(t[11]=r('<h4 class="font-bold text-xl mb-3" style="color:#f8fafc;" data-v-29b2d732>Online Student Clearance System</h4><p class="mb-4" style="color:#cbd5e1;" data-v-29b2d732>A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p><div class="flex flex-wrap gap-2" data-v-29b2d732><span class="px-3 py-1 text-sm rounded-full" style="background-color:#dc2626;color:#f8fafc;" data-v-29b2d732>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#374151;color:#f8fafc;" data-v-29b2d732>Vue.js</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#64748b;color:#f8fafc;" data-v-29b2d732>MySQL</span></div>',3))]),o("div",R,[o("div",A,[(d(),n("svg",W,t[12]||(t[12]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"},null,-1)])))]),t[13]||(t[13]=r('<h4 class="font-bold text-xl mb-3" style="color:#f8fafc;" data-v-29b2d732>Intern Tracker Tool</h4><p class="mb-4" style="color:#cbd5e1;" data-v-29b2d732>An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p><div class="flex flex-wrap gap-2" data-v-29b2d732><span class="px-3 py-1 text-sm rounded-full" style="background-color:#dc2626;color:#f8fafc;" data-v-29b2d732>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#374151;color:#f8fafc;" data-v-29b2d732>JavaScript</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#64748b;color:#f8fafc;" data-v-29b2d732>Bootstrap</span></div>',3))])])])]),t[15]||(t[15]=r('<section id="skills" class="py-20 px-8" style="background-color:#1a1a1a;" data-v-29b2d732><div class="max-w-6xl mx-auto" data-v-29b2d732><div class="text-center mb-16" data-v-29b2d732><h3 class="text-4xl font-bold mb-4" style="color:#f8fafc;" data-v-29b2d732>Skills &amp; Technologies</h3><p class="text-xl" style="color:#cbd5e1;" data-v-29b2d732>Technologies I work with to bring ideas to life</p></div><div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6" data-v-29b2d732><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-29b2d732><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#dc2626;" data-v-29b2d732><span class="font-bold text-lg" style="color:#f8fafc;" data-v-29b2d732>L</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-29b2d732>Laravel</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-29b2d732><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#374151;" data-v-29b2d732><span class="font-bold text-lg" style="color:#f8fafc;" data-v-29b2d732>V</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-29b2d732>Vue.js</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-29b2d732><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#64748b;" data-v-29b2d732><span class="font-bold text-lg" style="color:#f8fafc;" data-v-29b2d732>T</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-29b2d732>Tailwind CSS</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-29b2d732><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#dc2626;" data-v-29b2d732><span class="font-bold text-lg" style="color:#f8fafc;" data-v-29b2d732>U</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-29b2d732>UI/UX</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-29b2d732><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#374151;" data-v-29b2d732><span class="font-bold text-lg" style="color:#f8fafc;" data-v-29b2d732>G</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-29b2d732>Git</span></div></div></div></section>',1)),t[16]||(t[16]=o("section",{id:"contact",class:"py-20 px-8",style:{background:"linear-gradient(135deg, #dc2626 0%, #991b1b 100%)"}},[o("div",{class:"max-w-4xl mx-auto text-center"},[o("h3",{class:"text-4xl font-bold mb-6",style:{color:"#f8fafc"}},"Let's Work Together"),o("p",{class:"text-xl mb-8",style:{color:"#fca5a5"}},"Ready to bring your ideas to life? Let's discuss your next project."),o("div",{class:"flex flex-col sm:flex-row gap-4 justify-center"},[o("a",{href:"mailto:<EMAIL>",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{"background-color":"#f8fafc",color:"#dc2626"},onmouseover:"this.style.backgroundColor='#cbd5e1';",onmouseout:"this.style.backgroundColor='#f8fafc';"}," Send Email "),o("a",{href:"#",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{border:"2px solid #f8fafc",color:"#f8fafc","background-color":"transparent"},onmouseover:"this.style.backgroundColor='#f8fafc'; this.style.color='#dc2626';",onmouseout:"this.style.backgroundColor='transparent'; this.style.color='#f8fafc';"}," Download Resume ")])])],-1))])])}const O=x(M,[["render",X],["__scopeId","data-v-29b2d732"]]);export{O as default};
