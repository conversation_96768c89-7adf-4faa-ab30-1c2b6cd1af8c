import{_ as e}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{f as t,o as s,k as d}from"./app-CbOyJ0V7.js";const l={name:"Portfolio"},n={class:"min-h-screen bg-gray-100 text-gray-800"};function o(c,a,r,i,p,v){return s(),t("div",n,a[0]||(a[0]=[d('<header class="bg-white shadow p-6" data-v-6400d0e0><div class="max-w-7xl mx-auto flex justify-between items-center" data-v-6400d0e0><h1 class="text-2xl font-bold" data-v-6400d0e0><PERSON></h1><nav class="space-x-4" data-v-6400d0e0><a href="#projects" class="hover:text-blue-500" data-v-6400d0e0>Projects</a><a href="#skills" class="hover:text-blue-500" data-v-6400d0e0>Skills</a><a href="#contact" class="hover:text-blue-500" data-v-6400d0e0>Contact</a></nav></div></header><main class="p-8 max-w-5xl mx-auto" data-v-6400d0e0><section class="text-center my-12" data-v-6400d0e0><h2 class="text-4xl font-bold mb-4" data-v-6400d0e0>Hi, I’m a Web Developer</h2><p class="text-gray-600" data-v-6400d0e0>I create modern and responsive web applications using Laravel, Vue, and Tailwind.</p></section><section id="projects" class="my-16" data-v-6400d0e0><h3 class="text-2xl font-semibold mb-6" data-v-6400d0e0>Projects</h3><div class="grid md:grid-cols-2 gap-6" data-v-6400d0e0><div class="bg-white p-6 rounded-xl shadow" data-v-6400d0e0><h4 class="font-bold text-lg mb-2" data-v-6400d0e0>Online Student Clearance System</h4><p class="text-gray-600" data-v-6400d0e0>A web app for Xavier University designed for online student clearance submission and processing.</p></div><div class="bg-white p-6 rounded-xl shadow" data-v-6400d0e0><h4 class="font-bold text-lg mb-2" data-v-6400d0e0>Intern Tracker Tool</h4><p class="text-gray-600" data-v-6400d0e0>An internal tool for managing intern reports, attendance, and onboarding processes.</p></div></div></section><section id="skills" class="my-16" data-v-6400d0e0><h3 class="text-2xl font-semibold mb-6" data-v-6400d0e0>Skills</h3><div class="flex flex-wrap gap-4" data-v-6400d0e0><span class="px-4 py-2 bg-blue-100 text-blue-700 rounded-full" data-v-6400d0e0>Laravel</span><span class="px-4 py-2 bg-green-100 text-green-700 rounded-full" data-v-6400d0e0>Vue.js</span><span class="px-4 py-2 bg-purple-100 text-purple-700 rounded-full" data-v-6400d0e0>Tailwind CSS</span><span class="px-4 py-2 bg-yellow-100 text-yellow-700 rounded-full" data-v-6400d0e0>UI/UX</span><span class="px-4 py-2 bg-gray-200 text-gray-800 rounded-full" data-v-6400d0e0>Git</span></div></section><section id="contact" class="my-16 text-center" data-v-6400d0e0><h3 class="text-2xl font-semibold mb-6" data-v-6400d0e0>Contact</h3><p data-v-6400d0e0>Reach me via email at <a href="mailto:<EMAIL>" class="text-blue-500 underline" data-v-6400d0e0><EMAIL></a></p></section></main>',2)]))}const b=e(l,[["render",o],["__scopeId","data-v-6400d0e0"]]);export{b as default};
