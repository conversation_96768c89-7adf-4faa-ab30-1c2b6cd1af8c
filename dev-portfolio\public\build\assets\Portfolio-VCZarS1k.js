import{_ as b}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{f,o as m,b as e,k as x,n as u,F as h,l as w,e as y,m as c,p as k,q as S,s as j,t as M}from"./app-DHemo5VH.js";const T={name:"Portfolio",setup(){const l=c(!1),t=c(null),v=c(null),a=()=>{if(t.value){const o=t.value.offsetTop+t.value.offsetHeight,s=window.scrollY+100;l.value=s>=o}};return k(()=>{window.addEventListener("scroll",a),a()}),S(()=>{window.removeEventListener("scroll",a)}),{showStickyNav:l,heroSection:t,stickyNav:v,getParticleStyle:o=>{const s=Math.random()*4+1,n=Math.random()*100,i=Math.random()*20,r=Math.random()*10+10;return{width:`${s}px`,height:`${s}px`,left:`${n}%`,animationDelay:`${i}s`,animationDuration:`${r}s`}},getBinaryParticleStyle:o=>{const s=Math.random()*8+12,n=Math.random()*100,i=Math.random()*15,r=Math.random()*8+12;return{fontSize:`${s}px`,left:`${n}%`,animationDelay:`${i}s`,animationDuration:`${r}s`,color:`rgba(59, 130, 246, ${Math.random()*.5+.3})`}},scrollToSection:o=>{const s=document.getElementById(o);s&&s.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"})}}}},C={class:"bg-gray-100 text-gray-800"},$={class:"max-w-7xl mx-auto flex justify-between items-center p-4"},B={class:"space-x-6"},z={id:"hero",ref:"heroSection",class:"relative min-h-screen flex flex-col"},D={class:"flex-1 flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-slate-900 via-navy-900 to-slate-900"},I={class:"absolute inset-0 overflow-hidden"},L={class:"binary-particles"},P={class:"relative z-10 text-center text-white px-6 max-w-4xl"},V={class:"flex flex-col sm:flex-row gap-4 justify-center"};function N(l,t,v,a,g,p){return m(),f("div",C,[e("header",{ref:"stickyNav",class:u(["fixed top-0 left-0 right-0 z-50 bg-white shadow-lg transform -translate-y-full transition-transform duration-300",{"translate-y-0":a.showStickyNav}])},[e("div",$,[t[6]||(t[6]=e("h1",{class:"text-xl font-bold text-gray-800"},null,-1)),e("nav",B,[e("button",{onClick:t[0]||(t[0]=d=>a.scrollToSection("hero")),class:"text-gray-700 hover:text-blue-600 transition-colors"},"Home"),e("button",{onClick:t[1]||(t[1]=d=>a.scrollToSection("projects")),class:"text-gray-700 hover:text-blue-600 transition-colors"},"Projects"),e("button",{onClick:t[2]||(t[2]=d=>a.scrollToSection("skills")),class:"text-gray-700 hover:text-blue-600 transition-colors"},"Skills"),e("button",{onClick:t[3]||(t[3]=d=>a.scrollToSection("contact")),class:"text-gray-700 hover:text-blue-600 transition-colors"},"Contact")])])],2),e("section",z,[e("div",D,[e("div",I,[t[7]||(t[7]=x('<div class="floating-elements" data-v-cdf2d944><div class="element code-element element-1" data-v-cdf2d944>{ }</div><div class="element code-element element-2" data-v-cdf2d944>&lt;/&gt;</div><div class="element code-element element-3" data-v-cdf2d944>( )</div><div class="element code-element element-4" data-v-cdf2d944>[ ]</div><div class="element music-element element-5" data-v-cdf2d944>♪</div><div class="element music-element element-6" data-v-cdf2d944>♫</div><div class="element music-element element-7" data-v-cdf2d944>♬</div><div class="element music-element element-8" data-v-cdf2d944>♩</div><div class="element symbol-element element-9" data-v-cdf2d944>=&gt;</div><div class="element symbol-element element-10" data-v-cdf2d944>&amp;&amp;</div><div class="element symbol-element element-11" data-v-cdf2d944>#</div><div class="element symbol-element element-12" data-v-cdf2d944>$</div></div><div class="code-grid-background" data-v-cdf2d944></div>',2)),e("div",L,[(m(),f(h,null,w(30,d=>e("div",{class:"binary-particle",key:d,style:j(a.getBinaryParticleStyle(d))},M(Math.random()>.5?"1":"0"),5)),64))])]),e("div",P,[t[8]||(t[8]=e("h2",{class:"text-5xl md:text-7xl font-bold mb-6 leading-tight"},[y(" Hello! I am "),e("span",{class:"text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-blue-400 to-indigo-400"}," Isaac Martel. ")],-1)),t[9]||(t[9]=e("p",{class:"text-xl md:text-2xl mb-8 text-gray-200 leading-relaxed"}," I am a software engineer and full-stack web developer. ",-1)),e("div",V,[e("button",{onClick:t[4]||(t[4]=d=>a.scrollToSection("projects")),class:"bg-white/10 backdrop-blur-sm border border-white/20 text-white px-8 py-3 rounded-full font-semibold hover:bg-white/20 transition-all duration-300"}," View My Work "),e("button",{onClick:t[5]||(t[5]=d=>a.scrollToSection("contact")),class:"border-2 border-white/30 text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-navy-900 transition-all duration-300"}," Get In Touch ")])])]),t[10]||(t[10]=e("div",{class:"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce"},[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))],512),t[11]||(t[11]=x('<main class="bg-white" data-v-cdf2d944><section id="projects" class="py-20 px-8" data-v-cdf2d944><div class="max-w-6xl mx-auto" data-v-cdf2d944><div class="text-center mb-16" data-v-cdf2d944><h3 class="text-4xl font-bold mb-4 text-gray-800" data-v-cdf2d944>Featured Projects</h3><p class="text-xl text-gray-600" data-v-cdf2d944>Some of my recent work and contributions</p></div><div class="grid md:grid-cols-2 gap-8" data-v-cdf2d944><div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow border border-gray-100" data-v-cdf2d944><div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6" data-v-cdf2d944><svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-cdf2d944><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" data-v-cdf2d944></path></svg></div><h4 class="font-bold text-xl mb-3 text-gray-800" data-v-cdf2d944>Online Student Clearance System</h4><p class="text-gray-600 mb-4" data-v-cdf2d944>A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p><div class="flex flex-wrap gap-2" data-v-cdf2d944><span class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full" data-v-cdf2d944>Laravel</span><span class="px-3 py-1 bg-green-100 text-green-700 text-sm rounded-full" data-v-cdf2d944>Vue.js</span><span class="px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded-full" data-v-cdf2d944>MySQL</span></div></div><div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow border border-gray-100" data-v-cdf2d944><div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6" data-v-cdf2d944><svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-cdf2d944><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" data-v-cdf2d944></path></svg></div><h4 class="font-bold text-xl mb-3 text-gray-800" data-v-cdf2d944>Intern Tracker Tool</h4><p class="text-gray-600 mb-4" data-v-cdf2d944>An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p><div class="flex flex-wrap gap-2" data-v-cdf2d944><span class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full" data-v-cdf2d944>Laravel</span><span class="px-3 py-1 bg-yellow-100 text-yellow-700 text-sm rounded-full" data-v-cdf2d944>JavaScript</span><span class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full" data-v-cdf2d944>Bootstrap</span></div></div></div></div></section><section id="skills" class="py-20 px-8 bg-gray-50" data-v-cdf2d944><div class="max-w-6xl mx-auto" data-v-cdf2d944><div class="text-center mb-16" data-v-cdf2d944><h3 class="text-4xl font-bold mb-4 text-gray-800" data-v-cdf2d944>Skills &amp; Technologies</h3><p class="text-xl text-gray-600" data-v-cdf2d944>Technologies I work with to bring ideas to life</p></div><div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6" data-v-cdf2d944><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-cdf2d944><div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-cdf2d944><span class="text-red-600 font-bold text-lg" data-v-cdf2d944>L</span></div><span class="font-semibold text-gray-800" data-v-cdf2d944>Laravel</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-cdf2d944><div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-cdf2d944><span class="text-green-600 font-bold text-lg" data-v-cdf2d944>V</span></div><span class="font-semibold text-gray-800" data-v-cdf2d944>Vue.js</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-cdf2d944><div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-cdf2d944><span class="text-blue-600 font-bold text-lg" data-v-cdf2d944>T</span></div><span class="font-semibold text-gray-800" data-v-cdf2d944>Tailwind CSS</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-cdf2d944><div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-cdf2d944><span class="text-yellow-600 font-bold text-lg" data-v-cdf2d944>U</span></div><span class="font-semibold text-gray-800" data-v-cdf2d944>UI/UX</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-cdf2d944><div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-cdf2d944><span class="text-gray-600 font-bold text-lg" data-v-cdf2d944>G</span></div><span class="font-semibold text-gray-800" data-v-cdf2d944>Git</span></div></div></div></section><section id="contact" class="py-20 px-8 bg-gradient-to-br from-blue-600 to-purple-600" data-v-cdf2d944><div class="max-w-4xl mx-auto text-center" data-v-cdf2d944><h3 class="text-4xl font-bold mb-6 text-white" data-v-cdf2d944>Let&#39;s Work Together</h3><p class="text-xl text-blue-100 mb-8" data-v-cdf2d944>Ready to bring your ideas to life? Let&#39;s discuss your next project.</p><div class="flex flex-col sm:flex-row gap-4 justify-center" data-v-cdf2d944><a href="mailto:<EMAIL>" class="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors" data-v-cdf2d944> Send Email </a><a href="#" class="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-colors" data-v-cdf2d944> Download Resume </a></div></div></section></main>',1))])}const _=b(T,[["render",N],["__scopeId","data-v-cdf2d944"]]);export{_ as default};
