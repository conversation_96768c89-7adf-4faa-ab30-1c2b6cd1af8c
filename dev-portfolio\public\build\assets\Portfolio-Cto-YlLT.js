import{_ as g}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{f as b,o as m,b as e,k as o,n as h,F as u,l as f,m as c,p as w,q as y,s as k,t as S}from"./app-WBRLnLtg.js";const j={name:"Portfolio",setup(){const n=c(!1),a=c(null),v=c(null),t=()=>{if(a.value){const s=a.value.offsetTop+a.value.offsetHeight,l=window.scrollY+100;n.value=l>=s}};return w(()=>{window.addEventListener("scroll",t),t()}),y(()=>{window.removeEventListener("scroll",t)}),{showStickyNav:n,heroSection:a,stickyNav:v,getParticleStyle:s=>{const l=Math.random()*4+1,d=Math.random()*100,i=Math.random()*20,r=Math.random()*10+10;return{width:`${l}px`,height:`${l}px`,left:`${d}%`,animationDelay:`${i}s`,animationDuration:`${r}s`}},getBinaryParticleStyle:s=>{const l=Math.random()*8+12,d=Math.random()*100,i=Math.random()*15,r=Math.random()*8+12;return{fontSize:`${l}px`,left:`${d}%`,animationDelay:`${i}s`,animationDuration:`${r}s`,color:`rgba(30, 58, 138, ${Math.random()*.5+.3})`}}}}},M={class:"bg-gray-100 text-gray-800"},$={id:"hero",ref:"heroSection",class:"relative min-h-screen flex flex-col"},B={class:"flex-1 flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-slate-900 via-navy-900 to-slate-900"},D={class:"absolute inset-0 overflow-hidden"},L={class:"binary-particles"};function _(n,a,v,t,x,p){return m(),b("div",M,[e("header",{ref:"stickyNav",class:h(["fixed top-0 left-0 right-0 z-50 bg-white shadow-lg transform -translate-y-full transition-transform duration-300",{"translate-y-0":t.showStickyNav}])},a[0]||(a[0]=[o('<div class="max-w-7xl mx-auto flex justify-between items-center p-4" data-v-aa01e4b8><h1 class="text-xl font-bold text-gray-800" data-v-aa01e4b8></h1><nav class="space-x-6" data-v-aa01e4b8><a href="#hero" class="text-gray-700 hover:text-blue-600 transition-colors" data-v-aa01e4b8>Home</a><a href="#projects" class="text-gray-700 hover:text-blue-600 transition-colors" data-v-aa01e4b8>Projects</a><a href="#skills" class="text-gray-700 hover:text-blue-600 transition-colors" data-v-aa01e4b8>Skills</a><a href="#contact" class="text-gray-700 hover:text-blue-600 transition-colors" data-v-aa01e4b8>Contact</a></nav></div>',1)]),2),e("section",$,[e("div",B,[e("div",D,[a[1]||(a[1]=o('<div class="floating-elements" data-v-aa01e4b8><div class="element code-element element-1" data-v-aa01e4b8>{ }</div><div class="element code-element element-2" data-v-aa01e4b8>&lt;/&gt;</div><div class="element code-element element-3" data-v-aa01e4b8>( )</div><div class="element code-element element-4" data-v-aa01e4b8>[ ]</div><div class="element music-element element-5" data-v-aa01e4b8>♪</div><div class="element music-element element-6" data-v-aa01e4b8>♫</div><div class="element music-element element-7" data-v-aa01e4b8>♬</div><div class="element music-element element-8" data-v-aa01e4b8>♩</div><div class="element symbol-element element-9" data-v-aa01e4b8>=&gt;</div><div class="element symbol-element element-10" data-v-aa01e4b8>&amp;&amp;</div><div class="element symbol-element element-11" data-v-aa01e4b8>#</div><div class="element symbol-element element-12" data-v-aa01e4b8>$</div></div><div class="code-grid-background" data-v-aa01e4b8></div>',2)),e("div",L,[(m(),b(u,null,f(30,s=>e("div",{class:"binary-particle",key:s,style:k(t.getBinaryParticleStyle(s))},S(Math.random()>.5?"1":"0"),5)),64))])]),a[2]||(a[2]=o('<div class="relative z-10 text-center text-white px-6 max-w-4xl" data-v-aa01e4b8><h2 class="text-5xl md:text-7xl font-bold mb-6 leading-tight" data-v-aa01e4b8> Hi, I&#39;m a <span class="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-blue-400 to-indigo-400" data-v-aa01e4b8> Web Developer </span></h2><p class="text-xl md:text-2xl mb-8 text-gray-200 leading-relaxed" data-v-aa01e4b8> I create modern and responsive web applications using Laravel, Vue, and Tailwind CSS </p><div class="flex flex-col sm:flex-row gap-4 justify-center" data-v-aa01e4b8><a href="#projects" class="bg-white/10 backdrop-blur-sm border border-white/20 text-white px-8 py-3 rounded-full font-semibold hover:bg-white/20 transition-all duration-300" data-v-aa01e4b8> View My Work </a><a href="#contact" class="border-2 border-white/30 text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-navy-900 transition-all duration-300" data-v-aa01e4b8> Get In Touch </a></div></div>',1))]),a[3]||(a[3]=e("div",{class:"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce"},[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))],512),a[4]||(a[4]=o('<main class="bg-white" data-v-aa01e4b8><section id="projects" class="py-20 px-8" data-v-aa01e4b8><div class="max-w-6xl mx-auto" data-v-aa01e4b8><div class="text-center mb-16" data-v-aa01e4b8><h3 class="text-4xl font-bold mb-4 text-gray-800" data-v-aa01e4b8>Featured Projects</h3><p class="text-xl text-gray-600" data-v-aa01e4b8>Some of my recent work and contributions</p></div><div class="grid md:grid-cols-2 gap-8" data-v-aa01e4b8><div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow border border-gray-100" data-v-aa01e4b8><div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6" data-v-aa01e4b8><svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-aa01e4b8><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" data-v-aa01e4b8></path></svg></div><h4 class="font-bold text-xl mb-3 text-gray-800" data-v-aa01e4b8>Online Student Clearance System</h4><p class="text-gray-600 mb-4" data-v-aa01e4b8>A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p><div class="flex flex-wrap gap-2" data-v-aa01e4b8><span class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full" data-v-aa01e4b8>Laravel</span><span class="px-3 py-1 bg-green-100 text-green-700 text-sm rounded-full" data-v-aa01e4b8>Vue.js</span><span class="px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded-full" data-v-aa01e4b8>MySQL</span></div></div><div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow border border-gray-100" data-v-aa01e4b8><div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6" data-v-aa01e4b8><svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-aa01e4b8><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" data-v-aa01e4b8></path></svg></div><h4 class="font-bold text-xl mb-3 text-gray-800" data-v-aa01e4b8>Intern Tracker Tool</h4><p class="text-gray-600 mb-4" data-v-aa01e4b8>An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p><div class="flex flex-wrap gap-2" data-v-aa01e4b8><span class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full" data-v-aa01e4b8>Laravel</span><span class="px-3 py-1 bg-yellow-100 text-yellow-700 text-sm rounded-full" data-v-aa01e4b8>JavaScript</span><span class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full" data-v-aa01e4b8>Bootstrap</span></div></div></div></div></section><section id="skills" class="py-20 px-8 bg-gray-50" data-v-aa01e4b8><div class="max-w-6xl mx-auto" data-v-aa01e4b8><div class="text-center mb-16" data-v-aa01e4b8><h3 class="text-4xl font-bold mb-4 text-gray-800" data-v-aa01e4b8>Skills &amp; Technologies</h3><p class="text-xl text-gray-600" data-v-aa01e4b8>Technologies I work with to bring ideas to life</p></div><div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6" data-v-aa01e4b8><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-aa01e4b8><div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-aa01e4b8><span class="text-red-600 font-bold text-lg" data-v-aa01e4b8>L</span></div><span class="font-semibold text-gray-800" data-v-aa01e4b8>Laravel</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-aa01e4b8><div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-aa01e4b8><span class="text-green-600 font-bold text-lg" data-v-aa01e4b8>V</span></div><span class="font-semibold text-gray-800" data-v-aa01e4b8>Vue.js</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-aa01e4b8><div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-aa01e4b8><span class="text-blue-600 font-bold text-lg" data-v-aa01e4b8>T</span></div><span class="font-semibold text-gray-800" data-v-aa01e4b8>Tailwind CSS</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-aa01e4b8><div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-aa01e4b8><span class="text-yellow-600 font-bold text-lg" data-v-aa01e4b8>U</span></div><span class="font-semibold text-gray-800" data-v-aa01e4b8>UI/UX</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-aa01e4b8><div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-aa01e4b8><span class="text-gray-600 font-bold text-lg" data-v-aa01e4b8>G</span></div><span class="font-semibold text-gray-800" data-v-aa01e4b8>Git</span></div></div></div></section><section id="contact" class="py-20 px-8 bg-gradient-to-br from-blue-600 to-purple-600" data-v-aa01e4b8><div class="max-w-4xl mx-auto text-center" data-v-aa01e4b8><h3 class="text-4xl font-bold mb-6 text-white" data-v-aa01e4b8>Let&#39;s Work Together</h3><p class="text-xl text-blue-100 mb-8" data-v-aa01e4b8>Ready to bring your ideas to life? Let&#39;s discuss your next project.</p><div class="flex flex-col sm:flex-row gap-4 justify-center" data-v-aa01e4b8><a href="mailto:<EMAIL>" class="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors" data-v-aa01e4b8> Send Email </a><a href="#" class="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-colors" data-v-aa01e4b8> Download Resume </a></div></div></section></main>',1))])}const T=g(j,[["render",_],["__scopeId","data-v-aa01e4b8"]]);export{T as default};
