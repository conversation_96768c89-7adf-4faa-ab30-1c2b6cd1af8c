import{_ as u}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{f as r,o as n,b as o,n as b,e as v,k as d,l as c,m as p,p as x}from"./app-DcGa3OTu.js";const m={name:"Portfolio",setup(){const a=c(!1),e=c(null),i=c(null),t=()=>{if(e.value){const l=e.value.offsetTop+e.value.offsetHeight,s=window.scrollY+100;a.value=s>=l}};return p(()=>{window.addEventListener("scroll",t),t()}),x(()=>{window.removeEventListener("scroll",t)}),{showStickyNav:a,heroSection:e,stickyNav:i,scrollToSection:l=>{const s=document.getElementById(l);s&&s.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"})}}}},y={style:{"background-color":"#000000",color:"#f8fafc"}},g={class:"max-w-7xl mx-auto flex justify-center items-center p-4"},h={class:"space-x-8"},k={id:"hero",ref:"heroSection",class:"relative min-h-screen flex flex-col"},w={class:"flex-1 flex items-center justify-center relative hero-background"},C={class:"relative z-10 text-center px-6 max-w-4xl",style:{color:"#f8fafc"}},S={class:"flex flex-col sm:flex-row gap-4 justify-center"},j={style:{"background-color":"#000000"}},T={id:"projects",class:"py-20 px-8"},_={class:"max-w-6xl mx-auto"},I={class:"grid md:grid-cols-2 gap-8"},B={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"#000000",border:"2px solid #333333"}},L={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#2563eb"}},V={class:"w-6 h-6",style:{color:"#f8fafc"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},M={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"#000000",border:"2px solid #333333"}},N={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#1d4ed8"}},z={class:"w-6 h-6",style:{color:"#f8fafc"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"};function E(a,e,i,t,f,l){return n(),r("div",y,[o("header",{ref:"stickyNav",class:b(["fixed top-0 left-0 right-0 z-50 shadow-lg transform -translate-y-full transition-transform duration-300",{"translate-y-0":t.showStickyNav}]),style:{"background-color":"#000000"}},[o("div",g,[o("nav",h,[o("button",{onClick:e[0]||(e[0]=s=>t.scrollToSection("hero")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#3b82f6';",onmouseout:"this.style.color='#cbd5e1';"},"Home"),o("button",{onClick:e[1]||(e[1]=s=>t.scrollToSection("projects")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#3b82f6';",onmouseout:"this.style.color='#cbd5e1';"},"Projects"),o("button",{onClick:e[2]||(e[2]=s=>t.scrollToSection("skills")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#3b82f6';",onmouseout:"this.style.color='#cbd5e1';"},"Skills"),o("button",{onClick:e[3]||(e[3]=s=>t.scrollToSection("contact")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#3b82f6';",onmouseout:"this.style.color='#cbd5e1';"},"Contact")])])],2),o("section",k,[o("div",w,[e[8]||(e[8]=o("div",{class:"absolute inset-0 bg-black bg-opacity-50"},null,-1)),o("div",C,[e[6]||(e[6]=o("h2",{class:"text-5xl md:text-7xl font-bold mb-6 leading-tight"},[v(" Hello! I am "),o("span",{class:"text-transparent bg-clip-text",style:{background:"linear-gradient(45deg, #2563eb, #93c5fd)","-webkit-background-clip":"text","background-clip":"text"}}," Isaac Martel. ")],-1)),e[7]||(e[7]=o("p",{class:"text-xl md:text-2xl mb-8 leading-relaxed",style:{color:"#cbd5e1"}}," I am a software engineer and full-stack web developer. ",-1)),o("div",S,[o("button",{onClick:e[4]||(e[4]=s=>t.scrollToSection("projects")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300",style:{"background-color":"#2563eb",color:"#f8fafc",border:"2px solid #2563eb"},onmouseover:"this.style.backgroundColor='#1d4ed8'; this.style.borderColor='#1d4ed8';",onmouseout:"this.style.backgroundColor='#2563eb'; this.style.borderColor='#2563eb';"}," View My Work "),o("button",{onClick:e[5]||(e[5]=s=>t.scrollToSection("contact")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300",style:{"background-color":"transparent",color:"#f8fafc",border:"2px solid #3b82f6"},onmouseover:"this.style.backgroundColor='#3b82f6'; this.style.borderColor='#3b82f6';",onmouseout:"this.style.backgroundColor='transparent'; this.style.borderColor='#3b82f6';"}," Get In Touch ")])])]),e[9]||(e[9]=o("div",{class:"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce",style:{color:"#3b82f6"}},[o("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))],512),o("main",j,[o("section",T,[o("div",_,[e[14]||(e[14]=o("div",{class:"text-center mb-16"},[o("h3",{class:"text-4xl font-bold mb-4",style:{color:"#f8fafc"}},"Featured Projects"),o("p",{class:"text-xl",style:{color:"#cbd5e1"}},"Some of my recent work and contributions")],-1)),o("div",I,[o("div",B,[o("div",L,[(n(),r("svg",V,e[10]||(e[10]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)])))]),e[11]||(e[11]=d('<h4 class="font-bold text-xl mb-3" style="color:#f8fafc;" data-v-ee80f8f6>Online Student Clearance System</h4><p class="mb-4" style="color:#cbd5e1;" data-v-ee80f8f6>A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p><div class="flex flex-wrap gap-2" data-v-ee80f8f6><span class="px-3 py-1 text-sm rounded-full" style="background-color:#2563eb;color:#f8fafc;" data-v-ee80f8f6>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#3b82f6;color:#f8fafc;" data-v-ee80f8f6>Vue.js</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#60a5fa;color:#f8fafc;" data-v-ee80f8f6>MySQL</span></div>',3))]),o("div",M,[o("div",N,[(n(),r("svg",z,e[12]||(e[12]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"},null,-1)])))]),e[13]||(e[13]=d('<h4 class="font-bold text-xl mb-3" style="color:#f8fafc;" data-v-ee80f8f6>Intern Tracker Tool</h4><p class="mb-4" style="color:#cbd5e1;" data-v-ee80f8f6>An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p><div class="flex flex-wrap gap-2" data-v-ee80f8f6><span class="px-3 py-1 text-sm rounded-full" style="background-color:#2563eb;color:#f8fafc;" data-v-ee80f8f6>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#3b82f6;color:#f8fafc;" data-v-ee80f8f6>JavaScript</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#60a5fa;color:#f8fafc;" data-v-ee80f8f6>Bootstrap</span></div>',3))])])])]),e[15]||(e[15]=d('<section id="skills" class="py-20 px-8" style="background-color:#000000;" data-v-ee80f8f6><div class="max-w-6xl mx-auto" data-v-ee80f8f6><div class="text-center mb-16" data-v-ee80f8f6><h3 class="text-4xl font-bold mb-4" style="color:#f8fafc;" data-v-ee80f8f6>Skills &amp; Technologies</h3><p class="text-xl" style="color:#cbd5e1;" data-v-ee80f8f6>Technologies I work with to bring ideas to life</p></div><div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6" data-v-ee80f8f6><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#000000;border:1px solid #333333;" data-v-ee80f8f6><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#2563eb;" data-v-ee80f8f6><span class="font-bold text-lg" style="color:#f8fafc;" data-v-ee80f8f6>L</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-ee80f8f6>Laravel</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#000000;border:1px solid #333333;" data-v-ee80f8f6><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#3b82f6;" data-v-ee80f8f6><span class="font-bold text-lg" style="color:#f8fafc;" data-v-ee80f8f6>V</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-ee80f8f6>Vue.js</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#000000;border:1px solid #333333;" data-v-ee80f8f6><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#60a5fa;" data-v-ee80f8f6><span class="font-bold text-lg" style="color:#f8fafc;" data-v-ee80f8f6>T</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-ee80f8f6>Tailwind CSS</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#000000;border:1px solid #333333;" data-v-ee80f8f6><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#1d4ed8;" data-v-ee80f8f6><span class="font-bold text-lg" style="color:#f8fafc;" data-v-ee80f8f6>U</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-ee80f8f6>UI/UX</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#000000;border:1px solid #333333;" data-v-ee80f8f6><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#1e40af;" data-v-ee80f8f6><span class="font-bold text-lg" style="color:#f8fafc;" data-v-ee80f8f6>G</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-ee80f8f6>Git</span></div></div></div></section>',1)),e[16]||(e[16]=o("section",{id:"contact",class:"py-20 px-8",style:{"background-color":"#000000","border-top":"2px solid #ffffff"}},[o("div",{class:"max-w-4xl mx-auto text-center"},[o("h3",{class:"text-4xl font-bold mb-6",style:{color:"#f8fafc"}},"Let's Work Together"),o("p",{class:"text-xl mb-8",style:{color:"#cbd5e1"}},"Ready to bring your ideas to life? Let's discuss your next project."),o("div",{class:"flex flex-col sm:flex-row gap-4 justify-center"},[o("a",{href:"mailto:<EMAIL>",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{"background-color":"#2563eb",color:"#f8fafc"},onmouseover:"this.style.backgroundColor='#1d4ed8';",onmouseout:"this.style.backgroundColor='#2563eb';"}," Send Email "),o("a",{href:"#",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{border:"2px solid #2563eb",color:"#2563eb","background-color":"transparent"},onmouseover:"this.style.backgroundColor='#2563eb'; this.style.color='#f8fafc';",onmouseout:"this.style.backgroundColor='transparent'; this.style.color='#2563eb';"}," Download Resume ")])])],-1))])])}const H=u(m,[["render",E],["__scopeId","data-v-ee80f8f6"]]);export{H as default};
