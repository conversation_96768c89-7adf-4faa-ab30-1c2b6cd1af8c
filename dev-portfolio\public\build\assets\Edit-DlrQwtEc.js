import{_ as i}from"./AuthenticatedLayout-DPTXRSfq.js";import r from"./DeleteUserForm-c2tTiWS2.js";import m from"./UpdatePasswordForm-Cws3tc4q.js";import l from"./UpdateProfileInformationForm-CkNifJKs.js";import{f as d,o as n,a as t,u as c,g as p,w as o,b as s,F as u}from"./app-BPpXSKCC.js";import"./ApplicationLogo-z0wSpHtj.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./TextInput-CTcJrGtW.js";import"./PrimaryButton-Bit3JSnb.js";const _={class:"py-12"},f={class:"mx-auto max-w-7xl space-y-6 sm:px-6 lg:px-8"},g={class:"bg-white p-4 shadow sm:rounded-lg sm:p-8 dark:bg-gray-800"},x={class:"bg-white p-4 shadow sm:rounded-lg sm:p-8 dark:bg-gray-800"},h={class:"bg-white p-4 shadow sm:rounded-lg sm:p-8 dark:bg-gray-800"},N={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(y,e)=>(n(),d(u,null,[t(c(p),{title:"Profile"}),t(i,null,{header:o(()=>e[0]||(e[0]=[s("h2",{class:"text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200"}," Profile ",-1)])),default:o(()=>[s("div",_,[s("div",f,[s("div",g,[t(l,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",x,[t(m,{class:"max-w-xl"})]),s("div",h,[t(r,{class:"max-w-xl"})])])])]),_:1})],64))}};export{N as default};
