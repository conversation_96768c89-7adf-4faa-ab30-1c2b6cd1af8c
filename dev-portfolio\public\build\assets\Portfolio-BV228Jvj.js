import{_ as p}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{f as d,o as n,b as e,n as g,k as r,F as f,l as h,e as k,m as u,p as w,q as S,s as C,t as j}from"./app-Bh8_Wk_H.js";const M={name:"Portfolio",setup(){const c=u(!1),o=u(null),b=u(null),t=()=>{if(o.value){const l=o.value.offsetTop+o.value.offsetHeight,a=window.scrollY+100;c.value=a>=l}};return w(()=>{window.addEventListener("scroll",t),t()}),S(()=>{window.removeEventListener("scroll",t)}),{showStickyNav:c,heroSection:o,stickyNav:b,getParticleStyle:l=>{const a=Math.random()*4+1,i=Math.random()*100,m=Math.random()*20,v=Math.random()*10+10;return{width:`${a}px`,height:`${a}px`,left:`${i}%`,animationDelay:`${m}s`,animationDuration:`${v}s`}},getBinaryParticleStyle:l=>{const a=Math.random()*8+12,i=Math.random()*100,m=Math.random()*15,v=Math.random()*8+12;return{fontSize:`${a}px`,left:`${i}%`,animationDelay:`${m}s`,animationDuration:`${v}s`,color:`rgba(59, 130, 246, ${Math.random()*.5+.3})`}},scrollToSection:l=>{const a=document.getElementById(l);a&&a.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"})}}}},T={class:"bg-gray-100 text-gray-800"},_={class:"max-w-7xl mx-auto flex justify-between items-center p-4"},$={class:"space-x-6"},B={id:"hero",ref:"heroSection",class:"relative min-h-screen flex flex-col"},I={class:"flex-1 flex items-center justify-center relative overflow-hidden",style:{background:"linear-gradient(135deg, #0d1b2a 0%, #1b263b 50%, #415a77 100%)"}},z={class:"absolute inset-0 overflow-hidden"},D={class:"binary-particles"},L={class:"relative z-10 text-center text-white px-6 max-w-4xl"},P={class:"flex flex-col sm:flex-row gap-4 justify-center"},V={style:{"background-color":"#e0e1dd"}},N={id:"projects",class:"py-20 px-8"},E={class:"max-w-6xl mx-auto"},U={class:"grid md:grid-cols-2 gap-8"},H={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"white",border:"2px solid #778da9"}},A={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#415a77"}},F={class:"w-6 h-6",style:{color:"#e0e1dd"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},G={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"white",border:"2px solid #778da9"}},R={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#778da9"}},W={class:"w-6 h-6",style:{color:"#e0e1dd"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"};function X(c,o,b,t,y,x){return n(),d("div",T,[e("header",{ref:"stickyNav",class:g(["fixed top-0 left-0 right-0 z-50 shadow-lg transform -translate-y-full transition-transform duration-300",{"translate-y-0":t.showStickyNav}]),style:{"background-color":"#e0e1dd"}},[e("div",_,[o[6]||(o[6]=e("h1",{class:"text-xl font-bold",style:{color:"#0d1b2a"}},"Isaac Martel Abogatal",-1)),e("nav",$,[e("button",{onClick:o[0]||(o[0]=s=>t.scrollToSection("hero")),class:"transition-colors",style:{color:"#1b263b"},onmouseover:"this.style.color='#415a77';",onmouseout:"this.style.color='#1b263b';"},"Home"),e("button",{onClick:o[1]||(o[1]=s=>t.scrollToSection("projects")),class:"transition-colors",style:{color:"#1b263b"},onmouseover:"this.style.color='#415a77';",onmouseout:"this.style.color='#1b263b';"},"Projects"),e("button",{onClick:o[2]||(o[2]=s=>t.scrollToSection("skills")),class:"transition-colors",style:{color:"#1b263b"},onmouseover:"this.style.color='#415a77';",onmouseout:"this.style.color='#1b263b';"},"Skills"),e("button",{onClick:o[3]||(o[3]=s=>t.scrollToSection("contact")),class:"transition-colors",style:{color:"#1b263b"},onmouseover:"this.style.color='#415a77';",onmouseout:"this.style.color='#1b263b';"},"Contact")])])],2),e("section",B,[e("div",I,[e("div",z,[o[7]||(o[7]=r('<div class="floating-elements" data-v-a368ced3><div class="element code-element element-1" data-v-a368ced3>{ }</div><div class="element code-element element-2" data-v-a368ced3>&lt;/&gt;</div><div class="element code-element element-3" data-v-a368ced3>( )</div><div class="element code-element element-4" data-v-a368ced3>[ ]</div><div class="element music-element element-5" data-v-a368ced3>♪</div><div class="element music-element element-6" data-v-a368ced3>♫</div><div class="element music-element element-7" data-v-a368ced3>♬</div><div class="element music-element element-8" data-v-a368ced3>♩</div><div class="element symbol-element element-9" data-v-a368ced3>=&gt;</div><div class="element symbol-element element-10" data-v-a368ced3>&amp;&amp;</div><div class="element symbol-element element-11" data-v-a368ced3>#</div><div class="element symbol-element element-12" data-v-a368ced3>$</div></div><div class="code-grid-background" data-v-a368ced3></div>',2)),e("div",D,[(n(),d(f,null,h(30,s=>e("div",{class:"binary-particle",key:s,style:C(t.getBinaryParticleStyle(s))},j(Math.random()>.5?"1":"0"),5)),64))])]),e("div",L,[o[8]||(o[8]=e("h2",{class:"text-5xl md:text-7xl font-bold mb-6 leading-tight",style:{color:"#e0e1dd"}},[k(" Hello! I am "),e("span",{class:"text-transparent bg-clip-text",style:{background:"linear-gradient(45deg, #778da9, #415a77)","-webkit-background-clip":"text","background-clip":"text"}}," Isaac Martel. ")],-1)),o[9]||(o[9]=e("p",{class:"text-xl md:text-2xl mb-8 leading-relaxed",style:{color:"#e0e1dd",opacity:"0.9"}}," I am a software engineer and full-stack web developer. ",-1)),e("div",P,[e("button",{onClick:o[4]||(o[4]=s=>t.scrollToSection("projects")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300",style:{"background-color":"#415a77",color:"#e0e1dd",border:"2px solid #415a77"},onmouseover:"this.style.backgroundColor='#778da9'; this.style.borderColor='#778da9';",onmouseout:"this.style.backgroundColor='#415a77'; this.style.borderColor='#415a77';"}," View My Work "),e("button",{onClick:o[5]||(o[5]=s=>t.scrollToSection("contact")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300",style:{"background-color":"transparent",color:"#e0e1dd",border:"2px solid #778da9"},onmouseover:"this.style.backgroundColor='#778da9'; this.style.color='#0d1b2a';",onmouseout:"this.style.backgroundColor='transparent'; this.style.color='#e0e1dd';"}," Get In Touch ")])])]),o[10]||(o[10]=e("div",{class:"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce"},[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))],512),e("main",V,[e("section",N,[e("div",E,[o[15]||(o[15]=e("div",{class:"text-center mb-16"},[e("h3",{class:"text-4xl font-bold mb-4",style:{color:"#0d1b2a"}},"Featured Projects"),e("p",{class:"text-xl",style:{color:"#1b263b"}},"Some of my recent work and contributions")],-1)),e("div",U,[e("div",H,[e("div",A,[(n(),d("svg",F,o[11]||(o[11]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)])))]),o[12]||(o[12]=r('<h4 class="font-bold text-xl mb-3" style="color:#0d1b2a;" data-v-a368ced3>Online Student Clearance System</h4><p class="mb-4" style="color:#1b263b;" data-v-a368ced3>A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p><div class="flex flex-wrap gap-2" data-v-a368ced3><span class="px-3 py-1 text-sm rounded-full" style="background-color:#415a77;color:#e0e1dd;" data-v-a368ced3>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#778da9;color:#e0e1dd;" data-v-a368ced3>Vue.js</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#1b263b;color:#e0e1dd;" data-v-a368ced3>MySQL</span></div>',3))]),e("div",G,[e("div",R,[(n(),d("svg",W,o[13]||(o[13]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"},null,-1)])))]),o[14]||(o[14]=r('<h4 class="font-bold text-xl mb-3" style="color:#0d1b2a;" data-v-a368ced3>Intern Tracker Tool</h4><p class="mb-4" style="color:#1b263b;" data-v-a368ced3>An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p><div class="flex flex-wrap gap-2" data-v-a368ced3><span class="px-3 py-1 text-sm rounded-full" style="background-color:#415a77;color:#e0e1dd;" data-v-a368ced3>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#778da9;color:#e0e1dd;" data-v-a368ced3>JavaScript</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#1b263b;color:#e0e1dd;" data-v-a368ced3>Bootstrap</span></div>',3))])])])]),o[16]||(o[16]=r('<section id="skills" class="py-20 px-8" style="background-color:#778da9;" data-v-a368ced3><div class="max-w-6xl mx-auto" data-v-a368ced3><div class="text-center mb-16" data-v-a368ced3><h3 class="text-4xl font-bold mb-4" style="color:#e0e1dd;" data-v-a368ced3>Skills &amp; Technologies</h3><p class="text-xl" style="color:#e0e1dd;opacity:0.9;" data-v-a368ced3>Technologies I work with to bring ideas to life</p></div><div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6" data-v-a368ced3><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#e0e1dd;" data-v-a368ced3><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#415a77;" data-v-a368ced3><span class="font-bold text-lg" style="color:#e0e1dd;" data-v-a368ced3>L</span></div><span class="font-semibold" style="color:#0d1b2a;" data-v-a368ced3>Laravel</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#e0e1dd;" data-v-a368ced3><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#415a77;" data-v-a368ced3><span class="font-bold text-lg" style="color:#e0e1dd;" data-v-a368ced3>V</span></div><span class="font-semibold" style="color:#0d1b2a;" data-v-a368ced3>Vue.js</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#e0e1dd;" data-v-a368ced3><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#415a77;" data-v-a368ced3><span class="font-bold text-lg" style="color:#e0e1dd;" data-v-a368ced3>T</span></div><span class="font-semibold" style="color:#0d1b2a;" data-v-a368ced3>Tailwind CSS</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#e0e1dd;" data-v-a368ced3><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#415a77;" data-v-a368ced3><span class="font-bold text-lg" style="color:#e0e1dd;" data-v-a368ced3>U</span></div><span class="font-semibold" style="color:#0d1b2a;" data-v-a368ced3>UI/UX</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#e0e1dd;" data-v-a368ced3><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#415a77;" data-v-a368ced3><span class="font-bold text-lg" style="color:#e0e1dd;" data-v-a368ced3>G</span></div><span class="font-semibold" style="color:#0d1b2a;" data-v-a368ced3>Git</span></div></div></div></section>',1)),o[17]||(o[17]=e("section",{id:"contact",class:"py-20 px-8",style:{background:"linear-gradient(135deg, #1b263b 0%, #0d1b2a 100%)"}},[e("div",{class:"max-w-4xl mx-auto text-center"},[e("h3",{class:"text-4xl font-bold mb-6",style:{color:"#e0e1dd"}},"Let's Work Together"),e("p",{class:"text-xl mb-8",style:{color:"#778da9"}},"Ready to bring your ideas to life? Let's discuss your next project."),e("div",{class:"flex flex-col sm:flex-row gap-4 justify-center"},[e("a",{href:"mailto:<EMAIL>",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{"background-color":"#415a77",color:"#e0e1dd"},onmouseover:"this.style.backgroundColor='#778da9';",onmouseout:"this.style.backgroundColor='#415a77';"}," Send Email "),e("a",{href:"#",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{border:"2px solid #778da9",color:"#e0e1dd","background-color":"transparent"},onmouseover:"this.style.backgroundColor='#778da9'; this.style.color='#0d1b2a';",onmouseout:"this.style.backgroundColor='transparent'; this.style.color='#e0e1dd';"}," Download Resume ")])])],-1))])])}const O=p(M,[["render",X],["__scopeId","data-v-a368ced3"]]);export{O as default};
