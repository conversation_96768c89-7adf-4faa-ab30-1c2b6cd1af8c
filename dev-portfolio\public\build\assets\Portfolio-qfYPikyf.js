import{_ as b}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{f as c,o as n,b as t,n as u,k as a,e as v,l as d,m as p,p as x}from"./app-CRh9VI2h.js";const y={name:"Portfolio",setup(){const r=d(!1),o=d(null),i=d(null),e=()=>{if(o.value){const l=o.value.offsetTop+o.value.offsetHeight,s=window.scrollY+100;r.value=s>=l}};return p(()=>{window.addEventListener("scroll",e),e()}),x(()=>{window.removeEventListener("scroll",e)}),{showStickyNav:r,heroSection:o,stickyNav:i,scrollToSection:l=>{const s=document.getElementById(l);s&&s.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"})}}}},m={style:{"background-color":"#0a0a0a",color:"#f8fafc"}},g={class:"max-w-7xl mx-auto flex justify-center items-center p-4"},h={class:"space-x-8"},k={id:"hero",ref:"heroSection",class:"relative min-h-screen flex flex-col"},w={class:"flex-1 flex items-center justify-center relative",style:{background:"linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%)"}},C={class:"relative z-10 text-center px-6 max-w-4xl",style:{color:"#f8fafc"}},S={class:"flex flex-col sm:flex-row gap-4 justify-center"},j={style:{"background-color":"#0a0a0a"}},T={id:"projects",class:"py-20 px-8"},_={class:"max-w-6xl mx-auto"},I={class:"grid md:grid-cols-2 gap-8"},B={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"#1a1a1a",border:"2px solid #2a2a2a"}},L={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#dc2626"}},V={class:"w-6 h-6",style:{color:"#f8fafc"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},M={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"#1a1a1a",border:"2px solid #2a2a2a"}},N={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#374151"}},z={class:"w-6 h-6",style:{color:"#f8fafc"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"};function E(r,o,i,e,f,l){return n(),c("div",m,[t("header",{ref:"stickyNav",class:u(["fixed top-0 left-0 right-0 z-50 shadow-lg transform -translate-y-full transition-transform duration-300",{"translate-y-0":e.showStickyNav}]),style:{"background-color":"#1a1a1a"}},[t("div",g,[t("nav",h,[t("button",{onClick:o[0]||(o[0]=s=>e.scrollToSection("hero")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Home"),t("button",{onClick:o[1]||(o[1]=s=>e.scrollToSection("projects")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Projects"),t("button",{onClick:o[2]||(o[2]=s=>e.scrollToSection("skills")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Skills"),t("button",{onClick:o[3]||(o[3]=s=>e.scrollToSection("contact")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Contact")])])],2),t("section",k,[t("div",w,[o[8]||(o[8]=a('<div class="absolute inset-0 overflow-hidden" data-v-eb80403c><div class="absolute top-10 left-10 w-96 h-96 bg-red-600 rounded-full blur-3xl red-blur-1" style="opacity:0.2;" data-v-eb80403c></div><div class="absolute bottom-20 right-20 w-80 h-80 bg-red-500 rounded-full blur-3xl red-blur-2" style="opacity:0.15;" data-v-eb80403c></div><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-red-700 rounded-full blur-3xl red-blur-3" style="opacity:0.1;" data-v-eb80403c></div><div class="absolute top-1/4 right-1/4 w-48 h-48 bg-red-400 rounded-full blur-2xl red-blur-4" style="opacity:0.12;" data-v-eb80403c></div><div class="absolute bottom-1/4 left-1/4 w-56 h-56 bg-red-800 rounded-full blur-3xl red-blur-5" style="opacity:0.08;" data-v-eb80403c></div><div class="absolute top-3/4 right-10 w-40 h-40 bg-red-300 rounded-full blur-2xl red-blur-1" style="opacity:0.06;" data-v-eb80403c></div><div class="absolute top-10 right-1/3 w-32 h-32 bg-red-900 rounded-full blur-3xl red-blur-2" style="opacity:0.05;" data-v-eb80403c></div></div>',1)),t("div",C,[o[6]||(o[6]=t("h2",{class:"text-5xl md:text-7xl font-bold mb-6 leading-tight"},[v(" Hello! I am "),t("span",{class:"text-transparent bg-clip-text",style:{background:"linear-gradient(45deg, #dc2626, #fca5a5)","-webkit-background-clip":"text","background-clip":"text"}}," Isaac Martel. ")],-1)),o[7]||(o[7]=t("p",{class:"text-xl md:text-2xl mb-8 leading-relaxed",style:{color:"#cbd5e1"}}," I am a software engineer and full-stack web developer. ",-1)),t("div",S,[t("button",{onClick:o[4]||(o[4]=s=>e.scrollToSection("projects")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300",style:{"background-color":"#dc2626",color:"#f8fafc",border:"2px solid #dc2626"},onmouseover:"this.style.backgroundColor='#991b1b'; this.style.borderColor='#991b1b';",onmouseout:"this.style.backgroundColor='#dc2626'; this.style.borderColor='#dc2626';"}," View My Work "),t("button",{onClick:o[5]||(o[5]=s=>e.scrollToSection("contact")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300",style:{"background-color":"transparent",color:"#f8fafc",border:"2px solid #374151"},onmouseover:"this.style.backgroundColor='#374151'; this.style.borderColor='#374151';",onmouseout:"this.style.backgroundColor='transparent'; this.style.borderColor='#374151';"}," Get In Touch ")])])]),o[9]||(o[9]=t("div",{class:"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce"},[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))],512),t("main",j,[t("section",T,[t("div",_,[o[14]||(o[14]=t("div",{class:"text-center mb-16"},[t("h3",{class:"text-4xl font-bold mb-4",style:{color:"#f8fafc"}},"Featured Projects"),t("p",{class:"text-xl",style:{color:"#cbd5e1"}},"Some of my recent work and contributions")],-1)),t("div",I,[t("div",B,[t("div",L,[(n(),c("svg",V,o[10]||(o[10]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)])))]),o[11]||(o[11]=a('<h4 class="font-bold text-xl mb-3" style="color:#f8fafc;" data-v-eb80403c>Online Student Clearance System</h4><p class="mb-4" style="color:#cbd5e1;" data-v-eb80403c>A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p><div class="flex flex-wrap gap-2" data-v-eb80403c><span class="px-3 py-1 text-sm rounded-full" style="background-color:#dc2626;color:#f8fafc;" data-v-eb80403c>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#374151;color:#f8fafc;" data-v-eb80403c>Vue.js</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#64748b;color:#f8fafc;" data-v-eb80403c>MySQL</span></div>',3))]),t("div",M,[t("div",N,[(n(),c("svg",z,o[12]||(o[12]=[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"},null,-1)])))]),o[13]||(o[13]=a('<h4 class="font-bold text-xl mb-3" style="color:#f8fafc;" data-v-eb80403c>Intern Tracker Tool</h4><p class="mb-4" style="color:#cbd5e1;" data-v-eb80403c>An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p><div class="flex flex-wrap gap-2" data-v-eb80403c><span class="px-3 py-1 text-sm rounded-full" style="background-color:#dc2626;color:#f8fafc;" data-v-eb80403c>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#374151;color:#f8fafc;" data-v-eb80403c>JavaScript</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#64748b;color:#f8fafc;" data-v-eb80403c>Bootstrap</span></div>',3))])])])]),o[15]||(o[15]=a('<section id="skills" class="py-20 px-8" style="background-color:#1a1a1a;" data-v-eb80403c><div class="max-w-6xl mx-auto" data-v-eb80403c><div class="text-center mb-16" data-v-eb80403c><h3 class="text-4xl font-bold mb-4" style="color:#f8fafc;" data-v-eb80403c>Skills &amp; Technologies</h3><p class="text-xl" style="color:#cbd5e1;" data-v-eb80403c>Technologies I work with to bring ideas to life</p></div><div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6" data-v-eb80403c><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-eb80403c><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#dc2626;" data-v-eb80403c><span class="font-bold text-lg" style="color:#f8fafc;" data-v-eb80403c>L</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-eb80403c>Laravel</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-eb80403c><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#374151;" data-v-eb80403c><span class="font-bold text-lg" style="color:#f8fafc;" data-v-eb80403c>V</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-eb80403c>Vue.js</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-eb80403c><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#64748b;" data-v-eb80403c><span class="font-bold text-lg" style="color:#f8fafc;" data-v-eb80403c>T</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-eb80403c>Tailwind CSS</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-eb80403c><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#dc2626;" data-v-eb80403c><span class="font-bold text-lg" style="color:#f8fafc;" data-v-eb80403c>U</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-eb80403c>UI/UX</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-eb80403c><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#374151;" data-v-eb80403c><span class="font-bold text-lg" style="color:#f8fafc;" data-v-eb80403c>G</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-eb80403c>Git</span></div></div></div></section>',1)),o[16]||(o[16]=t("section",{id:"contact",class:"py-20 px-8",style:{background:"linear-gradient(135deg, #dc2626 0%, #991b1b 100%)"}},[t("div",{class:"max-w-4xl mx-auto text-center"},[t("h3",{class:"text-4xl font-bold mb-6",style:{color:"#f8fafc"}},"Let's Work Together"),t("p",{class:"text-xl mb-8",style:{color:"#fca5a5"}},"Ready to bring your ideas to life? Let's discuss your next project."),t("div",{class:"flex flex-col sm:flex-row gap-4 justify-center"},[t("a",{href:"mailto:<EMAIL>",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{"background-color":"#f8fafc",color:"#dc2626"},onmouseover:"this.style.backgroundColor='#cbd5e1';",onmouseout:"this.style.backgroundColor='#f8fafc';"}," Send Email "),t("a",{href:"#",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{border:"2px solid #f8fafc",color:"#f8fafc","background-color":"transparent"},onmouseover:"this.style.backgroundColor='#f8fafc'; this.style.color='#dc2626';",onmouseout:"this.style.backgroundColor='transparent'; this.style.color='#f8fafc';"}," Download Resume ")])])],-1))])])}const H=b(y,[["render",E],["__scopeId","data-v-eb80403c"]]);export{H as default};
