import{x as c,i as g,c as p,o as a,w as o,a as i,b as r,f as y,h as x,u as t,g as k,d as _,n as v,e as n,S as b}from"./app-BHpD2pUp.js";import{_ as h}from"./GuestLayout-Csp0kKtp.js";import{P as w}from"./PrimaryButton-CmYoddbQ.js";import"./ApplicationLogo-JncIgs9p.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const V={key:0,class:"mb-4 text-sm font-medium text-green-600 dark:text-green-400"},B={class:"mt-4 flex items-center justify-between"},L={__name:"VerifyEmail",props:{status:{type:String}},setup(d){const u=d,s=c({}),l=()=>{s.post(route("verification.send"))},f=g(()=>u.status==="verification-link-sent");return(m,e)=>(a(),p(h,null,{default:o(()=>[i(t(k),{title:"Email Verification"}),e[2]||(e[2]=r("div",{class:"mb-4 text-sm text-gray-600 dark:text-gray-400"}," Thanks for signing up! Before getting started, could you verify your email address by clicking on the link we just emailed to you? If you didn't receive the email, we will gladly send you another. ",-1)),f.value?(a(),y("div",V," A new verification link has been sent to the email address you provided during registration. ")):x("",!0),r("form",{onSubmit:_(l,["prevent"])},[r("div",B,[i(w,{class:v({"opacity-25":t(s).processing}),disabled:t(s).processing},{default:o(()=>e[0]||(e[0]=[n(" Resend Verification Email ")])),_:1,__:[0]},8,["class","disabled"]),i(t(b),{href:m.route("logout"),method:"post",as:"button",class:"rounded-md text-sm text-gray-600 underline hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:text-gray-400 dark:hover:text-gray-100 dark:focus:ring-offset-gray-800"},{default:o(()=>e[1]||(e[1]=[n("Log Out")])),_:1,__:[1]},8,["href"])])],32)]),_:1,__:[2]}))}};export{L as default};
