import{x as n,c as l,o as d,w as t,a as e,b as r,u as o,g as p,d as u,n as f,e as c}from"./app-Bl3zMDxa.js";import{_}from"./GuestLayout-DP-UYMBV.js";import{_ as w,a as b,b as g}from"./TextInput-CsWYj-3K.js";import{P as x}from"./PrimaryButton-CbP_rkJz.js";import"./ApplicationLogo-DsQLnixV.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const y={class:"mt-4 flex justify-end"},T={__name:"ConfirmPassword",setup(P){const s=n({password:""}),i=()=>{s.post(route("password.confirm"),{onFinish:()=>s.reset()})};return(V,a)=>(d(),l(_,null,{default:t(()=>[e(o(p),{title:"Confirm Password"}),a[2]||(a[2]=r("div",{class:"mb-4 text-sm text-gray-600 dark:text-gray-400"}," This is a secure area of the application. Please confirm your password before continuing. ",-1)),r("form",{onSubmit:u(i,["prevent"])},[r("div",null,[e(w,{for:"password",value:"Password"}),e(b,{id:"password",type:"password",class:"mt-1 block w-full",modelValue:o(s).password,"onUpdate:modelValue":a[0]||(a[0]=m=>o(s).password=m),required:"",autocomplete:"current-password",autofocus:""},null,8,["modelValue"]),e(g,{class:"mt-2",message:o(s).errors.password},null,8,["message"])]),r("div",y,[e(x,{class:f(["ms-4",{"opacity-25":o(s).processing}]),disabled:o(s).processing},{default:t(()=>a[1]||(a[1]=[c(" Confirm ")])),_:1,__:[1]},8,["class","disabled"])])],32)]),_:1,__:[2]}))}};export{T as default};
