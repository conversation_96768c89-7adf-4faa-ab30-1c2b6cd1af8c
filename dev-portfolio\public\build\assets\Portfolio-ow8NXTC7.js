import{_ as j}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{f as b,o as p,b as e,n as T,k as f,e as D,l as h,m as M,p as A}from"./app-BXK8RfwS.js";const L={name:"Portfolio",setup(){const v=h(!1),t=h(null),x=h(null),c=()=>{if(t.value){const r=t.value.offsetTop+t.value.offsetHeight,o=window.scrollY+100;v.value=o>=r}};M(()=>{window.addEventListener("scroll",c),c()}),A(()=>{window.removeEventListener("scroll",c)});const y=r=>{const o=Math.random()*4+1,d=Math.random()*100,s=Math.random()*20,a=Math.random()*10+10;return{width:`${o}px`,height:`${o}px`,left:`${d}%`,animationDelay:`${s}s`,animationDuration:`${a}s`}},g=r=>{const o=Math.random()*8+12,d=Math.random()*100,s=Math.random()*15,a=Math.random()*8+12;return{fontSize:`${o}px`,left:`${d}%`,animationDelay:`${s}s`,animationDuration:`${a}s`,color:`rgba(55, 65, 81, ${Math.random()*.5+.3})`}},m=r=>{const o=Math.random()*3+1,d=Math.random()*100,s=Math.random()*100,a=Math.random()*10,n=Math.random()*5+3,i=Math.random()*.8+.2;return{width:`${o}px`,height:`${o}px`,left:`${d}%`,top:`${s}%`,animationDelay:`${a}s`,animationDuration:`${n}s`,opacity:i}},C=r=>{const o=Math.random()*2+.5,d=Math.random()*100,s=Math.random()*20,a=Math.random()*15+10;return{width:`${o}px`,height:`${o}px`,left:`${d}%`,animationDelay:`${s}s`,animationDuration:`${a}s`}},k=()=>{const r=document.querySelectorAll(".element"),o=document.querySelector(".connection-lines");if(!o||r.length===0)return;o.innerHTML="";const d=Array.from(r).map(s=>{const a=s.getBoundingClientRect(),n=s.closest(".tech-elements").getBoundingClientRect();return{x:(a.left+a.width/2-n.left)/n.width*100,y:(a.top+a.height/2-n.top)/n.height*100,element:s}});for(let s=0;s<d.length;s++)for(let a=s+1;a<d.length;a++){const n=d[s],i=d[a],u=Math.sqrt((n.x-i.x)**2+(n.y-i.y)**2);if(u<30){const l=document.createElementNS("http://www.w3.org/2000/svg","line");l.setAttribute("x1",`${n.x}%`),l.setAttribute("y1",`${n.y}%`),l.setAttribute("x2",`${i.x}%`),l.setAttribute("y2",`${i.y}%`),l.setAttribute("stroke","rgba(148, 163, 184, 0.4)"),l.setAttribute("stroke-width","1"),l.setAttribute("opacity",Math.max(.3,1-u/30));const w=()=>{l.setAttribute("stroke","rgba(220, 38, 38, 0.8)"),l.setAttribute("stroke-width","2"),l.setAttribute("opacity","1"),l.classList.add("highlighted")},S=()=>{l.setAttribute("stroke","rgba(148, 163, 184, 0.4)"),l.setAttribute("stroke-width","1"),l.setAttribute("opacity",Math.max(.3,1-u/30)),l.classList.remove("highlighted")};n.element.addEventListener("mouseenter",w),i.element.addEventListener("mouseenter",w),n.element.addEventListener("mouseleave",S),i.element.addEventListener("mouseleave",S),o.appendChild(l)}}},$=r=>{const o=document.getElementById(r);o&&o.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"})};return M(()=>{setTimeout(()=>{k(),setInterval(k,5e3)},1e3)}),{showStickyNav:v,heroSection:t,stickyNav:x,getParticleStyle:y,getBinaryParticleStyle:g,getStarStyle:m,getDustParticleStyle:C,scrollToSection:$}}},B={style:{"background-color":"#0a0a0a",color:"#f8fafc"}},E={class:"max-w-7xl mx-auto flex justify-center items-center p-4"},z={class:"space-x-8"},I={id:"hero",ref:"heroSection",class:"relative min-h-screen flex flex-col"},_={class:"flex-1 flex items-center justify-center relative overflow-hidden",style:{background:"linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)"}},P={class:"relative z-10 text-center px-6 max-w-4xl",style:{color:"#f8fafc"}},V={class:"flex flex-col sm:flex-row gap-4 justify-center"},N={style:{"background-color":"#0a0a0a"}},H={id:"projects",class:"py-20 px-8"},R={class:"max-w-6xl mx-auto"},U={class:"grid md:grid-cols-2 gap-8"},q={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"#1a1a1a",border:"2px solid #2a2a2a"}},G={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#dc2626"}},W={class:"w-6 h-6",style:{color:"#f8fafc"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},X={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"#1a1a1a",border:"2px solid #2a2a2a"}},F={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#374151"}},J={class:"w-6 h-6",style:{color:"#f8fafc"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"};function O(v,t,x,c,y,g){return p(),b("div",B,[e("header",{ref:"stickyNav",class:T(["fixed top-0 left-0 right-0 z-50 shadow-lg transform -translate-y-full transition-transform duration-300",{"translate-y-0":c.showStickyNav}]),style:{"background-color":"#1a1a1a"}},[e("div",E,[e("nav",z,[e("button",{onClick:t[0]||(t[0]=m=>c.scrollToSection("hero")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Home"),e("button",{onClick:t[1]||(t[1]=m=>c.scrollToSection("projects")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Projects"),e("button",{onClick:t[2]||(t[2]=m=>c.scrollToSection("skills")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Skills"),e("button",{onClick:t[3]||(t[3]=m=>c.scrollToSection("contact")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#dc2626';",onmouseout:"this.style.color='#cbd5e1';"},"Contact")])])],2),e("section",I,[e("div",_,[t[8]||(t[8]=f('<div class="absolute inset-0 overflow-hidden" data-v-c37451d5><div class="tech-grid-background" data-v-c37451d5></div><div class="tech-elements" data-v-c37451d5><svg class="connection-lines" width="100%" height="100%" data-v-c37451d5></svg><div class="element tech-element element-1" data-v-c37451d5>{ }</div><div class="element tech-element element-2" data-v-c37451d5>&lt;/&gt;</div><div class="element tech-element element-3" data-v-c37451d5>( )</div><div class="element tech-element element-4" data-v-c37451d5>[ ]</div><div class="element tech-element element-5" data-v-c37451d5>=&gt;</div><div class="element tech-element element-6" data-v-c37451d5>&amp;&amp;</div><div class="element tech-element element-7" data-v-c37451d5>#</div><div class="element tech-element element-8" data-v-c37451d5>$</div><div class="element music-element element-9" data-v-c37451d5>♪</div><div class="element music-element element-10" data-v-c37451d5>♫</div><div class="element music-element element-11" data-v-c37451d5>♬</div><div class="element music-element element-12" data-v-c37451d5>♩</div><div class="element music-element element-13" data-v-c37451d5>♭</div><div class="element music-element element-14" data-v-c37451d5>♯</div><div class="element music-element element-15" data-v-c37451d5>𝄞</div><div class="element music-element element-16" data-v-c37451d5>𝄢</div></div><div class="accent-lines" data-v-c37451d5><div class="accent-line accent-line-1" data-v-c37451d5></div><div class="accent-line accent-line-2" data-v-c37451d5></div><div class="accent-line accent-line-3" data-v-c37451d5></div></div><div class="professional-glow" data-v-c37451d5></div></div>',1)),e("div",P,[t[6]||(t[6]=e("h2",{class:"text-5xl md:text-7xl font-bold mb-6 leading-tight"},[D(" Hello! I am "),e("span",{class:"text-transparent bg-clip-text",style:{background:"linear-gradient(45deg, #dc2626, #fca5a5)","-webkit-background-clip":"text","background-clip":"text"}}," Isaac Martel. ")],-1)),t[7]||(t[7]=e("p",{class:"text-xl md:text-2xl mb-8 leading-relaxed",style:{color:"#cbd5e1"}}," I am a software engineer and full-stack web developer. ",-1)),e("div",V,[e("button",{onClick:t[4]||(t[4]=m=>c.scrollToSection("projects")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300",style:{"background-color":"#dc2626",color:"#f8fafc",border:"2px solid #dc2626"},onmouseover:"this.style.backgroundColor='#991b1b'; this.style.borderColor='#991b1b';",onmouseout:"this.style.backgroundColor='#dc2626'; this.style.borderColor='#dc2626';"}," View My Work "),e("button",{onClick:t[5]||(t[5]=m=>c.scrollToSection("contact")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300",style:{"background-color":"transparent",color:"#f8fafc",border:"2px solid #374151"},onmouseover:"this.style.backgroundColor='#374151'; this.style.borderColor='#374151';",onmouseout:"this.style.backgroundColor='transparent'; this.style.borderColor='#374151';"}," Get In Touch ")])])]),t[9]||(t[9]=e("div",{class:"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce"},[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))],512),e("main",N,[e("section",H,[e("div",R,[t[14]||(t[14]=e("div",{class:"text-center mb-16"},[e("h3",{class:"text-4xl font-bold mb-4",style:{color:"#f8fafc"}},"Featured Projects"),e("p",{class:"text-xl",style:{color:"#cbd5e1"}},"Some of my recent work and contributions")],-1)),e("div",U,[e("div",q,[e("div",G,[(p(),b("svg",W,t[10]||(t[10]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)])))]),t[11]||(t[11]=f('<h4 class="font-bold text-xl mb-3" style="color:#f8fafc;" data-v-c37451d5>Online Student Clearance System</h4><p class="mb-4" style="color:#cbd5e1;" data-v-c37451d5>A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p><div class="flex flex-wrap gap-2" data-v-c37451d5><span class="px-3 py-1 text-sm rounded-full" style="background-color:#dc2626;color:#f8fafc;" data-v-c37451d5>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#374151;color:#f8fafc;" data-v-c37451d5>Vue.js</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#64748b;color:#f8fafc;" data-v-c37451d5>MySQL</span></div>',3))]),e("div",X,[e("div",F,[(p(),b("svg",J,t[12]||(t[12]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"},null,-1)])))]),t[13]||(t[13]=f('<h4 class="font-bold text-xl mb-3" style="color:#f8fafc;" data-v-c37451d5>Intern Tracker Tool</h4><p class="mb-4" style="color:#cbd5e1;" data-v-c37451d5>An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p><div class="flex flex-wrap gap-2" data-v-c37451d5><span class="px-3 py-1 text-sm rounded-full" style="background-color:#dc2626;color:#f8fafc;" data-v-c37451d5>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#374151;color:#f8fafc;" data-v-c37451d5>JavaScript</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#64748b;color:#f8fafc;" data-v-c37451d5>Bootstrap</span></div>',3))])])])]),t[15]||(t[15]=f('<section id="skills" class="py-20 px-8" style="background-color:#1a1a1a;" data-v-c37451d5><div class="max-w-6xl mx-auto" data-v-c37451d5><div class="text-center mb-16" data-v-c37451d5><h3 class="text-4xl font-bold mb-4" style="color:#f8fafc;" data-v-c37451d5>Skills &amp; Technologies</h3><p class="text-xl" style="color:#cbd5e1;" data-v-c37451d5>Technologies I work with to bring ideas to life</p></div><div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6" data-v-c37451d5><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-c37451d5><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#dc2626;" data-v-c37451d5><span class="font-bold text-lg" style="color:#f8fafc;" data-v-c37451d5>L</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-c37451d5>Laravel</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-c37451d5><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#374151;" data-v-c37451d5><span class="font-bold text-lg" style="color:#f8fafc;" data-v-c37451d5>V</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-c37451d5>Vue.js</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-c37451d5><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#64748b;" data-v-c37451d5><span class="font-bold text-lg" style="color:#f8fafc;" data-v-c37451d5>T</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-c37451d5>Tailwind CSS</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-c37451d5><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#dc2626;" data-v-c37451d5><span class="font-bold text-lg" style="color:#f8fafc;" data-v-c37451d5>U</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-c37451d5>UI/UX</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#2a2a2a;" data-v-c37451d5><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#374151;" data-v-c37451d5><span class="font-bold text-lg" style="color:#f8fafc;" data-v-c37451d5>G</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-c37451d5>Git</span></div></div></div></section>',1)),t[16]||(t[16]=e("section",{id:"contact",class:"py-20 px-8",style:{background:"linear-gradient(135deg, #dc2626 0%, #991b1b 100%)"}},[e("div",{class:"max-w-4xl mx-auto text-center"},[e("h3",{class:"text-4xl font-bold mb-6",style:{color:"#f8fafc"}},"Let's Work Together"),e("p",{class:"text-xl mb-8",style:{color:"#fca5a5"}},"Ready to bring your ideas to life? Let's discuss your next project."),e("div",{class:"flex flex-col sm:flex-row gap-4 justify-center"},[e("a",{href:"mailto:<EMAIL>",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{"background-color":"#f8fafc",color:"#dc2626"},onmouseover:"this.style.backgroundColor='#cbd5e1';",onmouseout:"this.style.backgroundColor='#f8fafc';"}," Send Email "),e("a",{href:"#",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{border:"2px solid #f8fafc",color:"#f8fafc","background-color":"transparent"},onmouseover:"this.style.backgroundColor='#f8fafc'; this.style.color='#dc2626';",onmouseout:"this.style.backgroundColor='transparent'; this.style.color='#f8fafc';"}," Download Resume ")])])],-1))])])}const K=j(L,[["render",O],["__scopeId","data-v-c37451d5"]]);export{K as default};
