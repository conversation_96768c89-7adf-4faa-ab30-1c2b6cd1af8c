import{_ as x}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{f,o as b,b as o,n as y,k as u,l as a,m as h,p as g}from"./app-CTdrURyD.js";const k={name:"Portfolio",setup(){const d=a(!1),t=a(null),v=a(null),e=a(null),n=a(null),r=a(null),s=()=>{if(t.value){const c=t.value.offsetTop+t.value.offsetHeight,l=window.scrollY+100;d.value=l>=c}};h(()=>{window.addEventListener("scroll",s),s(),setTimeout(()=>{m()},500)}),g(()=>{window.removeEventListener("scroll",s)});const i=(c,l=0)=>new Promise(p=>{setTimeout(()=>{c.classList.add("animate-in"),p()},l)}),m=async()=>{e.value&&n.value&&r.value&&(e.value.classList.remove("animate-in"),n.value.classList.remove("animate-in"),r.value.classList.remove("animate-in"),await i(e.value,300),await i(n.value,600),await i(r.value,1e3))};return{showStickyNav:d,heroSection:t,stickyNav:v,heroLine1:e,heroLine2:n,heroSubtitle:r,scrollToSection:c=>{const l=document.getElementById(c);l&&l.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"})}}}},w={style:{"background-color":"#000000",color:"#f8fafc"}},S={class:"max-w-7xl mx-auto flex justify-center items-center p-4"},C={class:"space-x-8"},_={id:"hero",ref:"heroSection",class:"relative min-h-screen flex flex-col"},j={class:"flex-1 flex items-center justify-center relative hero-background"},T={class:"relative z-10 text-center px-6 max-w-4xl",style:{color:"#f8fafc"}},L={class:"text-5xl md:text-7xl font-bold mb-6 leading-tight hero-title"},I={ref:"heroLine1",class:"hero-line hero-line-1"},B={ref:"heroLine2",class:"text-transparent bg-clip-text hero-line hero-line-2",style:{background:"linear-gradient(45deg, #2563eb, #93c5fd)","-webkit-background-clip":"text","background-clip":"text"}},M={ref:"heroSubtitle",class:"text-xl md:text-2xl mb-8 leading-relaxed hero-subtitle",style:{color:"#cbd5e1"}},V={class:"flex flex-col sm:flex-row gap-4 justify-center"},z={style:{"background-color":"#000000"}},E={id:"projects",class:"py-20 px-8"},N={class:"max-w-6xl mx-auto"},P={class:"grid md:grid-cols-2 gap-8"},H={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"#000000",border:"2px solid #333333"}},U={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#2563eb"}},A={class:"w-6 h-6",style:{color:"#f8fafc"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},G={class:"p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow",style:{"background-color":"#000000",border:"2px solid #333333"}},R={class:"w-12 h-12 rounded-lg flex items-center justify-center mb-6",style:{"background-color":"#1d4ed8"}},$={class:"w-6 h-6",style:{color:"#f8fafc"},fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"};function W(d,t,v,e,n,r){return b(),f("div",w,[o("header",{ref:"stickyNav",class:y(["fixed top-0 left-0 right-0 z-50 shadow-lg transform -translate-y-full transition-transform duration-300",{"translate-y-0":e.showStickyNav}]),style:{"background-color":"#000000"}},[o("div",S,[o("nav",C,[o("button",{onClick:t[0]||(t[0]=s=>e.scrollToSection("hero")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#3b82f6';",onmouseout:"this.style.color='#cbd5e1';"},"Home"),o("button",{onClick:t[1]||(t[1]=s=>e.scrollToSection("projects")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#3b82f6';",onmouseout:"this.style.color='#cbd5e1';"},"Projects"),o("button",{onClick:t[2]||(t[2]=s=>e.scrollToSection("skills")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#3b82f6';",onmouseout:"this.style.color='#cbd5e1';"},"Skills"),o("button",{onClick:t[3]||(t[3]=s=>e.scrollToSection("contact")),class:"transition-colors",style:{color:"#cbd5e1"},onmouseover:"this.style.color='#3b82f6';",onmouseout:"this.style.color='#cbd5e1';"},"Contact")])])],2),o("section",_,[o("div",j,[t[6]||(t[6]=o("div",{class:"absolute inset-0 bg-black bg-opacity-50"},null,-1)),o("div",T,[o("h2",L,[o("span",I,"Hello! I am",512),o("span",B," Isaac Martel. ",512)]),o("p",M," I am a software engineer and full-stack web developer. ",512),o("div",V,[o("button",{onClick:t[4]||(t[4]=s=>e.scrollToSection("projects")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300",style:{"background-color":"#2563eb",color:"#f8fafc",border:"2px solid #2563eb"},onmouseover:"this.style.backgroundColor='#1d4ed8'; this.style.borderColor='#1d4ed8';",onmouseout:"this.style.backgroundColor='#2563eb'; this.style.borderColor='#2563eb';"}," View My Work "),o("button",{onClick:t[5]||(t[5]=s=>e.scrollToSection("contact")),class:"px-8 py-3 rounded-full font-semibold transition-all duration-300",style:{"background-color":"transparent",color:"#f8fafc",border:"2px solid #3b82f6"},onmouseover:"this.style.backgroundColor='#3b82f6'; this.style.borderColor='#3b82f6';",onmouseout:"this.style.backgroundColor='transparent'; this.style.borderColor='#3b82f6';"}," Get In Touch ")])])]),t[7]||(t[7]=o("div",{class:"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce",style:{color:"#3b82f6"}},[o("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 14l-7 7m0 0l-7-7m7 7V3"})])],-1))],512),o("main",z,[o("section",E,[o("div",N,[t[12]||(t[12]=o("div",{class:"text-center mb-16"},[o("h3",{class:"text-4xl font-bold mb-4",style:{color:"#f8fafc"}},"Featured Projects"),o("p",{class:"text-xl",style:{color:"#cbd5e1"}},"Some of my recent work and contributions")],-1)),o("div",P,[o("div",H,[o("div",U,[(b(),f("svg",A,t[8]||(t[8]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)])))]),t[9]||(t[9]=u('<h4 class="font-bold text-xl mb-3" style="color:#f8fafc;" data-v-c00079ba>Online Student Clearance System</h4><p class="mb-4" style="color:#cbd5e1;" data-v-c00079ba>A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p><div class="flex flex-wrap gap-2" data-v-c00079ba><span class="px-3 py-1 text-sm rounded-full" style="background-color:#2563eb;color:#f8fafc;" data-v-c00079ba>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#3b82f6;color:#f8fafc;" data-v-c00079ba>Vue.js</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#60a5fa;color:#f8fafc;" data-v-c00079ba>MySQL</span></div>',3))]),o("div",G,[o("div",R,[(b(),f("svg",$,t[10]||(t[10]=[o("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"},null,-1)])))]),t[11]||(t[11]=u('<h4 class="font-bold text-xl mb-3" style="color:#f8fafc;" data-v-c00079ba>Intern Tracker Tool</h4><p class="mb-4" style="color:#cbd5e1;" data-v-c00079ba>An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p><div class="flex flex-wrap gap-2" data-v-c00079ba><span class="px-3 py-1 text-sm rounded-full" style="background-color:#2563eb;color:#f8fafc;" data-v-c00079ba>Laravel</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#3b82f6;color:#f8fafc;" data-v-c00079ba>JavaScript</span><span class="px-3 py-1 text-sm rounded-full" style="background-color:#60a5fa;color:#f8fafc;" data-v-c00079ba>Bootstrap</span></div>',3))])])])]),t[13]||(t[13]=u('<section id="skills" class="py-20 px-8" style="background-color:#000000;" data-v-c00079ba><div class="max-w-6xl mx-auto" data-v-c00079ba><div class="text-center mb-16" data-v-c00079ba><h3 class="text-4xl font-bold mb-4" style="color:#f8fafc;" data-v-c00079ba>Skills &amp; Technologies</h3><p class="text-xl" style="color:#cbd5e1;" data-v-c00079ba>Technologies I work with to bring ideas to life</p></div><div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6" data-v-c00079ba><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#000000;border:1px solid #333333;" data-v-c00079ba><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#2563eb;" data-v-c00079ba><span class="font-bold text-lg" style="color:#f8fafc;" data-v-c00079ba>L</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-c00079ba>Laravel</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#000000;border:1px solid #333333;" data-v-c00079ba><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#3b82f6;" data-v-c00079ba><span class="font-bold text-lg" style="color:#f8fafc;" data-v-c00079ba>V</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-c00079ba>Vue.js</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#000000;border:1px solid #333333;" data-v-c00079ba><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#60a5fa;" data-v-c00079ba><span class="font-bold text-lg" style="color:#f8fafc;" data-v-c00079ba>T</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-c00079ba>Tailwind CSS</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#000000;border:1px solid #333333;" data-v-c00079ba><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#1d4ed8;" data-v-c00079ba><span class="font-bold text-lg" style="color:#f8fafc;" data-v-c00079ba>U</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-c00079ba>UI/UX</span></div><div class="p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" style="background-color:#000000;border:1px solid #333333;" data-v-c00079ba><div class="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-4" style="background-color:#1e40af;" data-v-c00079ba><span class="font-bold text-lg" style="color:#f8fafc;" data-v-c00079ba>G</span></div><span class="font-semibold" style="color:#f8fafc;" data-v-c00079ba>Git</span></div></div></div></section>',1)),t[14]||(t[14]=o("section",{id:"contact",class:"py-20 px-8",style:{background:"linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)"}},[o("div",{class:"max-w-4xl mx-auto text-center"},[o("h3",{class:"text-4xl font-bold mb-6",style:{color:"#f8fafc"}},"Let's Work Together"),o("p",{class:"text-xl mb-8",style:{color:"#93c5fd"}},"Ready to bring your ideas to life? Let's discuss your next project."),o("div",{class:"flex flex-col sm:flex-row gap-4 justify-center"},[o("a",{href:"mailto:<EMAIL>",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{"background-color":"#f8fafc",color:"#2563eb"},onmouseover:"this.style.backgroundColor='#cbd5e1';",onmouseout:"this.style.backgroundColor='#f8fafc';"}," Send Email "),o("a",{href:"#",class:"px-8 py-3 rounded-full font-semibold transition-colors",style:{border:"2px solid #f8fafc",color:"#f8fafc","background-color":"transparent"},onmouseover:"this.style.backgroundColor='#f8fafc'; this.style.color='#2563eb';",onmouseout:"this.style.backgroundColor='transparent'; this.style.color='#f8fafc';"}," Download Resume ")])])],-1))])])}const J=x(k,[["render",W],["__scopeId","data-v-c00079ba"]]);export{J as default};
