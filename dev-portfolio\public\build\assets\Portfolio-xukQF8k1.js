import{_ as i}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{f as c,o as v,b as n,k as s,n as x,l as d,m as p,p as g}from"./app-EgtmYQF5.js";const b={name:"Portfolio",setup(){const e=d(!1),a=d(null),o=d(null),t=()=>{if(a.value){const l=a.value.offsetTop+a.value.offsetHeight,r=window.scrollY+100;e.value=r>=l}};return p(()=>{window.addEventListener("scroll",t),t()}),g(()=>{window.removeEventListener("scroll",t)}),{showStickyNav:e,heroSection:a,stickyNav:o}}},h={class:"bg-gray-100 text-gray-800"},u={id:"hero",ref:"heroSection",class:"relative min-h-screen flex flex-col"};function f(e,a,o,t,l,r){return v(),c("div",h,[n("header",{ref:"stickyNav",class:x(["fixed top-0 left-0 right-0 z-50 bg-white shadow-lg transform -translate-y-full transition-transform duration-300",{"translate-y-0":t.showStickyNav}])},a[0]||(a[0]=[s('<div class="max-w-7xl mx-auto flex justify-between items-center p-4" data-v-669325ad><h1 class="text-xl font-bold text-gray-800" data-v-669325ad>Isaac Martel Abogatal</h1><nav class="space-x-6" data-v-669325ad><a href="#hero" class="text-gray-700 hover:text-blue-600 transition-colors" data-v-669325ad>Home</a><a href="#projects" class="text-gray-700 hover:text-blue-600 transition-colors" data-v-669325ad>Projects</a><a href="#skills" class="text-gray-700 hover:text-blue-600 transition-colors" data-v-669325ad>Skills</a><a href="#contact" class="text-gray-700 hover:text-blue-600 transition-colors" data-v-669325ad>Contact</a></nav></div>',1)]),2),n("section",u,a[1]||(a[1]=[s('<div class="flex-1 flex items-center justify-center bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800" data-v-669325ad><div class="text-center text-white px-6 max-w-4xl" data-v-669325ad><h2 class="text-5xl md:text-7xl font-bold mb-6 leading-tight" data-v-669325ad> Hi, I&#39;m a <span class="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-pink-400" data-v-669325ad> Web Developer </span></h2><p class="text-xl md:text-2xl mb-8 text-gray-200 leading-relaxed" data-v-669325ad> I create modern and responsive web applications using Laravel, Vue, and Tailwind CSS </p><div class="flex flex-col sm:flex-row gap-4 justify-center" data-v-669325ad><a href="#projects" class="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors" data-v-669325ad> View My Work </a><a href="#contact" class="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-colors" data-v-669325ad> Get In Touch </a></div></div></div><div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce" data-v-669325ad><svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-669325ad><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" data-v-669325ad></path></svg></div>',2)]),512),a[2]||(a[2]=s('<main class="bg-white" data-v-669325ad><section id="projects" class="py-20 px-8" data-v-669325ad><div class="max-w-6xl mx-auto" data-v-669325ad><div class="text-center mb-16" data-v-669325ad><h3 class="text-4xl font-bold mb-4 text-gray-800" data-v-669325ad>Featured Projects</h3><p class="text-xl text-gray-600" data-v-669325ad>Some of my recent work and contributions</p></div><div class="grid md:grid-cols-2 gap-8" data-v-669325ad><div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow border border-gray-100" data-v-669325ad><div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6" data-v-669325ad><svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-669325ad><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" data-v-669325ad></path></svg></div><h4 class="font-bold text-xl mb-3 text-gray-800" data-v-669325ad>Online Student Clearance System</h4><p class="text-gray-600 mb-4" data-v-669325ad>A comprehensive web application for Xavier University designed for online student clearance submission and processing, streamlining administrative workflows.</p><div class="flex flex-wrap gap-2" data-v-669325ad><span class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full" data-v-669325ad>Laravel</span><span class="px-3 py-1 bg-green-100 text-green-700 text-sm rounded-full" data-v-669325ad>Vue.js</span><span class="px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded-full" data-v-669325ad>MySQL</span></div></div><div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow border border-gray-100" data-v-669325ad><div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6" data-v-669325ad><svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-669325ad><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" data-v-669325ad></path></svg></div><h4 class="font-bold text-xl mb-3 text-gray-800" data-v-669325ad>Intern Tracker Tool</h4><p class="text-gray-600 mb-4" data-v-669325ad>An internal management tool for tracking intern reports, attendance, and onboarding processes, improving HR efficiency and intern experience.</p><div class="flex flex-wrap gap-2" data-v-669325ad><span class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full" data-v-669325ad>Laravel</span><span class="px-3 py-1 bg-yellow-100 text-yellow-700 text-sm rounded-full" data-v-669325ad>JavaScript</span><span class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full" data-v-669325ad>Bootstrap</span></div></div></div></div></section><section id="skills" class="py-20 px-8 bg-gray-50" data-v-669325ad><div class="max-w-6xl mx-auto" data-v-669325ad><div class="text-center mb-16" data-v-669325ad><h3 class="text-4xl font-bold mb-4 text-gray-800" data-v-669325ad>Skills &amp; Technologies</h3><p class="text-xl text-gray-600" data-v-669325ad>Technologies I work with to bring ideas to life</p></div><div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6" data-v-669325ad><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-669325ad><div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-669325ad><span class="text-red-600 font-bold text-lg" data-v-669325ad>L</span></div><span class="font-semibold text-gray-800" data-v-669325ad>Laravel</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-669325ad><div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-669325ad><span class="text-green-600 font-bold text-lg" data-v-669325ad>V</span></div><span class="font-semibold text-gray-800" data-v-669325ad>Vue.js</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-669325ad><div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-669325ad><span class="text-blue-600 font-bold text-lg" data-v-669325ad>T</span></div><span class="font-semibold text-gray-800" data-v-669325ad>Tailwind CSS</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-669325ad><div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-669325ad><span class="text-yellow-600 font-bold text-lg" data-v-669325ad>U</span></div><span class="font-semibold text-gray-800" data-v-669325ad>UI/UX</span></div><div class="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow text-center" data-v-669325ad><div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4" data-v-669325ad><span class="text-gray-600 font-bold text-lg" data-v-669325ad>G</span></div><span class="font-semibold text-gray-800" data-v-669325ad>Git</span></div></div></div></section><section id="contact" class="py-20 px-8 bg-gradient-to-br from-blue-600 to-purple-600" data-v-669325ad><div class="max-w-4xl mx-auto text-center" data-v-669325ad><h3 class="text-4xl font-bold mb-6 text-white" data-v-669325ad>Let&#39;s Work Together</h3><p class="text-xl text-blue-100 mb-8" data-v-669325ad>Ready to bring your ideas to life? Let&#39;s discuss your next project.</p><div class="flex flex-col sm:flex-row gap-4 justify-center" data-v-669325ad><a href="mailto:<EMAIL>" class="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors" data-v-669325ad> Send Email </a><a href="#" class="border-2 border-white text-white px-8 py-3 rounded-full font-semibold hover:bg-white hover:text-blue-600 transition-colors" data-v-669325ad> Download Resume </a></div></div></section></main>',1))])}const y=i(b,[["render",f],["__scopeId","data-v-669325ad"]]);export{y as default};
